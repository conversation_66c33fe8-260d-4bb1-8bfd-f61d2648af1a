import request from '@/utils/request'

//查询
export function projectPlanList(data, params) {
  return request({
    url: '/finance/doublearea/plan/projectPlanList',
    method: 'post',
    data,
    params
  })
}

//00 修改状态为未确认
export function updateStatusInfo(data) {
  return request({
    url: '/finance/doublearea/planedit/updateStatusInfo',
    method: 'post',
    data
  })
}

//01 查询项目收支计划管理：基本信息
export function queryPlanBaseInfo(data) {
  return request({
    url: '/finance/doublearea/planedit/queryPlanBaseInfo',
    method: 'post',
    data
  })
}

//02 查询项目收支计划管理：补充信息
export function queryPlanSupplementInfo(data) {
  return request({
    url: '/finance/doublearea/planedit/queryPlanSupplementInfo',
    method: 'post',
    data
  })
}

//03 补充信息：新增/编辑 弹框查询
export function supplementQueryInfo(data) {
  return request({
    url: '/finance/doublearea/planedit/supplementQueryInfo',
    method: 'post',
    data
  })
}

//04 补充信息：（保存/提交
export function supplementEditSubmit(data) {
  return request({
    url: '/finance/doublearea/planedit/supplementEditSubmit',
    method: 'post',
    data
  })
}

//04 补充信息：保存
export function supplementEditSave(data) {
  return request({
    url: '/finance/doublearea/planedit/supplementEditSave',
    method: 'post',
    data
  })
}


//05 补充信息： 删除补充信息
export function dropSupplementInfo(data) {
  return request({
    url: '/finance/doublearea/planedit/dropSupplementInfo',
    method: 'post',
    data
  })
}

//07 最终提交
export function submitInfo(data) {
  return request({
    url: '/finance/doublearea/planedit/submitInfo',
    method: 'post',
    data
  })
}

//08 最终保存
export function saveInfo(data) {
  return request({
    url: '/finance/doublearea/planedit/saveInfo',
    method: 'post',
    data
  })
}
