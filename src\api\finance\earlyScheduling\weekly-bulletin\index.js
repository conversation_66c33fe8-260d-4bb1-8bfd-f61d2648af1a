import request from '@/utils/request'

// 周通报列表
export function weeklyBulletinList(params, data) {
  return request({
    url: '/finance/warningWeek/weeklyBulletinList',
    method: 'post',
    data: data,
    params: params
  })
}

// 周通报详情列表
export function weeklyBulletinDetailList(params, data) {
  return request({
    url: '/finance/warningWeek/weeklyBulletinDetailList',
    method: 'post',
    data: data,
    params: params
  })
}

// 周通报详情通知公示列表
export function weeklyNoticeBulletinDetailList(params, data) {
  return request({
    url: '/finance/warningWeek/weeklyNoticeBulletinDetailList',
    method: 'post',
    data: data,
    params: params
  })
}
