import request from '@/utils/request'

const BASE_HEADER_URL = `/group/knowledgeBase`

export class GroupKnowledgeLibrary {
  /**
   * @description 获取数据列表
   * @param params
   * @return {*}
   */
  static getDataList (params) {
    return request({
      url: `${BASE_HEADER_URL}/querying/files`,
      method: 'post',
      data: params
    })
  }

  /**
   * @description 上传文件
   * @param params
   * @return {*}
   */
  static upload (params) {
    return request({
      url: `${BASE_HEADER_URL}/uploadFiles`,
      method: 'post',
      data: params
    })
  }

  /**
   * @description 删除文件
   * @param id
   * @return {*}
   */
  static del (id) {
    return request({
      url: `${BASE_HEADER_URL}/deleteFile/${id}`,
      method: 'post'
    })
  }
}
