import request from '@/utils/request'

// 获取案例表主键
export function getSolutionId() {
  return request({
    url: '/library/solution/getSolutionId',
    method: 'post',
  })
}

export function getFileList(params){
  return request({
    url: '/library/solution/selectSolutionFiles',
    method: 'post',
    params: params
  })
}

export function deleteSolutionFile(id){
  return request({
    url: '/library/solution/deleteSolutionFile/' + id,
    method: 'post',
  })
}

// 查询案例库列表
export function listSolution(params,page) {
  return request({
    url: '/library/solution/list',
    method: 'post',
    params: page,
    data:params
  })
}

// 查询案例库详细
export function getSolution(params) {
  return request({
    url: '/library/solution/getSolutionInfo' ,
    method: 'post',
    data:params
  })
}

/* export function getSuitDepts(query) {
  return request({
    url: '/library/solution/getSuitDepts',
    method: 'post',
    params: query
  })
} */

export function saveSolutionInfo(param) {
  return request({
    url: '/library/solution/saveSolutionInfo',
    method: 'post',
    data: param
  })
}

export function releaseSolutionInfo(param) {
  return request({
    url: '/library/solution/releaseSolutionInfo',
    method: 'post',
    data: param
  })
}

export function delSolutionInfo(param) {
  return request({
    url: '/library/solution/delSolutionInfo',
    method: 'post',
    data: param
  })
}
export function updateSolutionStatus(param) {
  return request({
    url: '/library/solution/updateSolutionStatus',
    method: 'post',
    data: param
  })
}





