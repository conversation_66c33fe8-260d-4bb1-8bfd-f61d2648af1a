import request from '@/utils/request'

// 查询列表
export function listReport(query) {
  return request({
    url: '/finance/receivable/reportDetails/list',
    method: 'post',
    data: query,
    params: {
      pageNum:query.pageNum,
      pageSize:query.pageSize
    }
  })
}

//09 获取已计收应收账款明细查询
export function getInfo(query) {
  return request({
    url: '/finance/receivedDetails/insertReceivedDetail/'+query,
    method: 'post'
  })
}

//10 获取已计收基本信息
export function getInfoToDo(id) {
  return request({
    url: '/finance/receivedDetails/getReceivedDetailsInfo/'+id,
    method: 'post'
  })
}


//09 获取未计收基本信息
export function getInfoPayment(id,type) {
  return request({
    url: '/finance/uncollectedDetails/selectUncollectedDetail/'+id+'/'+type,
    method: 'post'
  })
}

//10 获取已计收基本信息
export function getInfoPaymentTodo(id) {
  return request({
    url: '/finance/receivedDetails/getReceivedDetailsInfo/'+id,
    method: 'post'
  })
}

//10 获取未计收基本信息
export function getUncollectedProjectInfoById(id) {
  return request({
    url: '/finance/uncollected/getUncollectedProjectInfoById/'+id,
    method: 'post'
  })
}

//10 获取未计收基本信息-待办
export function getUncollectedDetailsInfo(id) {
  return request({
    url: '/finance/uncollectedDetails/getUncollectedDetailsInfo/'+id,
    method: 'post'
  })
}

//09 上一次填报信息查询--未计收
export function selectLastUncollectedInfo(collectId,flowDetailType) {
  return request({
    url: '/finance/uncollectedDetails/selectLastUncollectedInfo/'+collectId+'/'+flowDetailType,
    method: 'post'
  })
}

// 10 历史调度信息查询
export function queryHistortInfo(query,type) {
  return request({
    url: '/finance/dispatch/queryHistoryInfoById/'+query+'/'+type,
    method: 'post'
  })
}

//  09 本次调度信息查询
export function queryThisDispatchDetailInfo(query,type) {
  return request({
    url: '/finance/dispatch/getNewCollectionInfo/'+query+'/'+type,
    method: 'post'
  })
}

//  10 本次调度信息查询
export function queryThisDispatchDetailInfoToDo(data) {
  return request({
    url: '/finance/dispatch/getCurrentCollectionInfo',
    method: 'post',
    data:data
  })
}

//编辑时-已计收数据
export function selectCollectedDetail(id,flowDetailType) {
  return request({
    url: '/finance/receivedDetails/selectCollectedDetail/'+id+'/'+flowDetailType,
    method: 'post'
  })
}

//新增时-已计收数据
export function selectCollectedInfo(id) {
  return request({
    url: '/finance/receivedDetails/selectCollectedInfo/'+id,
    method: 'post'
  })
}

// 15 项目信息-新增-根据计收单号查询应收账款信息
export function insertReceivedDetail(id) {
  return request({
    url: '/finance/receivedDetails/insertReceivedDetail/'+id,
    method: 'post'
  })
}

// 18 应收账款明细新增催收信息
export function getReceivedInfoId() {
  return request({
    url: '/finance/receivedDetails/getReceivedInfoId',
    method: 'post'
  })
}

// 12 保存应收账款明细
export function save(data) {
  return request({
    url: '/finance/uncollectedDetails/updateUncollectedDetails',
    method: 'post',
    data: data
  })
}

//保存已计收
export function updateCollectedDetails(data) {
  return request({
    url: '/finance/receivedDetails/updateCollectedDetails',
    method: 'post',
    data: data
  })
}

//提交已计收
export function submitCollectedDetails(data) {
  return request({
    url: '/finance/receivedDetails/submitCollectedDetails',
    method: 'post',
    data: data
  })
}

//上一次填报信息查询--已计收
export function selectLastCollectedInfo(collectId,flowDetailType) {
  return request({
    url: '/finance/receivedDetails/selectLastCollectedInfo/'+collectId+'/'+flowDetailType,
    method: 'post'
  })
}


// 保存与校验接口
export function submitUncollectedDetails(data) {
  return request({
    url: '/finance/uncollectedDetails/submitUncollectedDetails',
    method: 'post',
    data: data
  })
}

// 13 提交应收账款明细
export function validateAndSave(data) {
  return request({
    url: '/finance/receivable/reportDetails/validateAndSave',
    method: 'put',
    data: data
  })
}

// 13 提交应收账款明细
export function saveReceivedInfoById(data) {
  return request({
    url: '/finance/receivedDetails/saveReceivedInfoById',
    method: 'post',
    data: data
  })
}

// 查询附件列表
export function queryFileList(data) {
  return request({
    url: '/sys/attachment/queryAttachmentList',
    method: 'post',
    data: data
  })
}

// 删除附件
export function dropFilesById(data) {
  return request({
    url: '/sys/attachment/delAttachmentById/'+data,
    method: 'post'
  })
}
