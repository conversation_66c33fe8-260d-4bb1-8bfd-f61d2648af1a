import request from '@/utils/request'

// 获取案例表主键
export function getCaseId() {
  return request({
    url: '/library/case/getCaseId',
    method: 'post',
  })
}

export function getFileList(params){
  return request({
    url: '/library/case/selectCaseFiles',
    method: 'post',
    params: params
  })
}

export function deleteCaseFile(id){
  return request({
    url: '/library/case/deleteCaseFile/' + id,
    method: 'post',
  })
}

// 查询案例库列表
export function listCase(params,page) {
  return request({
    url: '/library/case/list',
    method: 'post',
    params: page,
    data:params
  })
}

// 查询案例库详细
export function getCase(params) {
  return request({
    url: '/library/case/getCaseInfo' ,
    method: 'post',
    data:params
  })
}

export function getSuitDepts(query) {
  return request({
    url: '/library/case/getSuitDepts',
    method: 'post',
    params: query
  })
}

export function saveCaseInfo(param) {
  return request({
    url: '/library/case/saveCaseInfo',
    method: 'post',
    data: param
  })
}

export function releaseCaseInfo(param) {
  return request({
    url: '/library/case/releaseCaseInfo',
    method: 'post',
    data: param
  })
}

export function delCaseInfo(param) {
  return request({
    url: '/library/case/delCaseInfo',
    method: 'post',
    data: param
  })
}
export function updateCaseStatus(param) {
  return request({
    url: '/library/case/updateCaseStatus',
    method: 'post',
    data: param
  })
}





