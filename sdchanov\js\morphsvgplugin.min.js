/*!
 * VERSION: 0.9.0
 * DATE: 2018-12-11
 * UPDATES AND DOCS AT: http://greensock.com
 *
 * @license Copyright (c) 2008-2018, GreenSock. All rights reserved.
 * MorphSVGPlugin is a Club GreenSock membership benefit; You must have a valid membership to use
 * this code without violating the terms of use. Visit http://greensock.com/club/ to sign up or get more details.
 * This work is subject to the software agreement that was issued with your membership.
 *
 * @author: <PERSON>, <EMAIL>
 */
var _gsScope = "undefined" != typeof module && module.exports && "undefined" != typeof global ? global : this || window;
(_gsScope._gsQueue || (_gsScope._gsQueue = [])).push(function () {
        "use strict";
        var e = Math.PI,
            E = e / 180,
            M = /[achlmqstvz]|(-?\d*\.?\d*(?:e[\-+]?\d+)?)[0-9]/gi,
            A = /(?:(-|-=|\+=)?\d*\.?\d*(?:e[\-+]?\d+)?)[0-9]/gi,
            a = /(^[#\.][a-z]|[a-y][a-z])/gi,
            w = /[achlmqstvz]/gi,
            y = /[\+\-]?\d*\.?\d+e[\+\-]?\d+/gi,
            r = 2 * e,
            u = _gsScope._gsDefine.globals.TweenLite,
            x = "MorphSVGPlugin",
            z = function (t) {
                _gsScope.console && console.log(t)
            },
            L = function (t, e, r, o, n, i, a, h, s) {
                if (t !== h || e !== s) {
                    r = Math.abs(r), o = Math.abs(o);
                    var l = Math.sqrt,
                        g = Math.cos,
                        f = Math.sin,
                        u = 2 * Math.PI,
                        p = n % 360 * E,
                        c = g(p),
                        d = f(p),
                        m = (t - h) / 2,
                        C = (e - s) / 2,
                        _ = c * m + d * C,
                        v = -d * m + c * C,
                        S = _ * _,
                        b = v * v,
                        M = S / (r * r) + b / (o * o);
                    1 < M && (r = l(M) * r, o = l(M) * o);
                    var w = r * r,
                        y = o * o,
                        A = (w * y - w * b - y * S) / (w * b + y * S);
                    A < 0 && (A = 0);
                    var x = (i === a ? -1 : 1) * l(A),
                        T = x * (r * v / o),
                        N = x * (-o * _ / r),
                        P = (t + h) / 2 + (c * T - d * N),
                        z = (e + s) / 2 + (d * T + c * N),
                        L = (_ - T) / r,
                        I = (v - N) / o,
                        G = (-_ - T) / r,
                        R = (-v - N) / o,
                        Y = L * L + I * I,
                        j = (I < 0 ? -1 : 1) * Math.acos(L / l(Y)),
                        q = (L * R - I * G < 0 ? -1 : 1) * Math.acos((L * G + I * R) / l(Y * (G * G + R * R)));
                    !a && 0 < q ? q -= u : a && q < 0 && (q += u), j %= u, q %= u;
                    var B, D = Math.ceil(Math.abs(q) / (u / 4)),
                        X = [],
                        O = q / D,
                        V = 4 / 3 * f(O / 2) / (1 + g(O / 2)),
                        F = c * r,
                        H = d * r,
                        Q = d * -o,
                        U = c * o;
                    for (B = 0; B < D; B++) _ = g(n = j + B * O), v = f(n), L = g(n += O), I = f(n), X.push(_ - V * v, v + V * _, L + V * I, I - V * L, L, I);
                    for (B = 0; B < X.length; B += 2) _ = X[B], v = X[B + 1], X[B] = _ * F + v * Q + P, X[B + 1] = _ * H + v * U + z;
                    return X[B - 2] = h, X[B - 1] = s, X
                }
            },
            I = function (t) {
                var e, r, o, n, i, a, h, s, l, g, f, u, p, c = (t + "").replace(y, function (t) {
                        var e = +t;
                        return e < 1e-4 && -1e-4 < e ? 0 : e
                    }).match(M) || [],
                    d = [],
                    m = 0,
                    C = 0,
                    _ = c.length,
                    v = 0,
                    S = "ERROR: malformed path: " + t,
                    b = function (t, e, r, o) {
                        g = (r - t) / 3, f = (o - e) / 3, h.push(t + g, e + f, r - g, o - f, r, o)
                    };
                if (!t || !isNaN(c[0]) || isNaN(c[1])) return console.log(S), d;
                for (e = 0; e < _; e++)
                    if (p = i, isNaN(c[e]) ? a = (i = c[e].toUpperCase()) !== c[e] : e--, o = +c[e + 1], n = +c[e + 2], a && (o += m, n += C), e || (s = o, l = n), "M" === i) h && (h.length < 8 ? d.length -= 1 : v += h.length), m = s = o, C = l = n, h = [o, n], d.push(h), e += 2, i = "L";
                    else if ("C" === i) h || (h = [0, 0]), a || (m = C = 0), h.push(o, n, m + 1 * c[e + 3], C + 1 * c[e + 4], m += 1 * c[e + 5], C += 1 * c[e + 6]), e += 6;
                else if ("S" === i) g = m, f = C, "C" !== p && "S" !== p || (g += m - h[h.length - 4], f += C - h[h.length - 3]), a || (m = C = 0), h.push(g, f, o, n, m += 1 * c[e + 3], C += 1 * c[e + 4]), e += 4;
                else if ("Q" === i) g = m + 2 / 3 * (o - m), f = C + 2 / 3 * (n - C), a || (m = C = 0), m += 1 * c[e + 3], C += 1 * c[e + 4], h.push(g, f, m + 2 / 3 * (o - m), C + 2 / 3 * (n - C), m, C), e += 4;
                else if ("T" === i) g = m - h[h.length - 4], f = C - h[h.length - 3], h.push(m + g, C + f, o + 2 / 3 * (m + 1.5 * g - o), n + 2 / 3 * (C + 1.5 * f - n), m = o, C = n), e += 2;
                else if ("H" === i) b(m, C, m = o, C), e += 1;
                else if ("V" === i) b(m, C, m, C = o + (a ? C - m : 0)), e += 1;
                else if ("L" === i || "Z" === i) "Z" === i && (o = s, n = l, h.closed = !0), ("L" === i || .5 < Math.abs(m - o) || .5 < Math.abs(C - n)) && (b(m, C, o, n), "L" === i && (e += 2)), m = o, C = n;
                else if ("A" === i) {
                    if (u = L(m, C, +c[e + 1], +c[e + 2], +c[e + 3], +c[e + 4], +c[e + 5], (a ? m : 0) + 1 * c[e + 6], (a ? C : 0) + 1 * c[e + 7]))
                        for (r = 0; r < u.length; r++) h.push(u[r]);
                    m = h[h.length - 2], C = h[h.length - 1], e += 7
                } else console.log(S);
                return e = h.length, h[0] === h[e - 2] && h[1] === h[e - 1] && (h.closed = !0), d.totalPoints = v + e, d
            },
            b = function (t, e) {
                var r, o, n, i, a, h, s, l, g, f, u, p, c, d, m = 0,
                    C = t.length,
                    _ = e / ((C - 2) / 6);
                for (c = 2; c < C; c += 6)
                    for (m += _; .999999 < m;) r = t[c - 2], o = t[c - 1], n = t[c], i = t[c + 1], a = t[c + 2], h = t[c + 3], s = t[c + 4], l = t[c + 5], g = r + (n - r) * (d = 1 / (Math.floor(m) + 1)), g += ((u = n + (a - n) * d) - g) * d, u += (a + (s - a) * d - u) * d, f = o + (i - o) * d, f += ((p = i + (h - i) * d) - f) * d, p += (h + (l - h) * d - p) * d, t.splice(c, 4, r + (n - r) * d, o + (i - o) * d, g, f, g + (u - g) * d, f + (p - f) * d, u, p, a + (s - a) * d, h + (l - h) * d), c += 6, C += 6, m--;
                return t
            },
            G = function (t, e) {
                var r, o, n, i = "",
                    a = t.length,
                    h = Math.pow(10, e || 2);
                for (o = 0; o < t.length; o++) {
                    for (a = (n = t[o]).length, i += "M" + n[0] + " " + n[1] + " C", r = 2; r < a; r++) i += (n[r] * h | 0) / h + " ";
                    n.closed && (i += "z")
                }
                return i
            },
            R = function (t) {
                for (var e = [], r = t.length - 1, o = 0; - 1 < --r;) e[o++] = t[r], e[o++] = t[r + 1], r--;
                for (r = 0; r < o; r++) t[r] = e[r];
                t.reversed = !t.reversed
            },
            p = function (t) {
                var e, r = t.length,
                    o = 0,
                    n = 0;
                for (e = 0; e < r; e++) o += t[e++], n += t[e];
                return [o / (r / 2), n / (r / 2)]
            },
            Y = function (t) {
                var e, r, o, n = t.length,
                    i = t[0],
                    a = i,
                    h = t[1],
                    s = h;
                for (o = 6; o < n; o += 6) i < (e = t[o]) ? i = e : e < a && (a = e), h < (r = t[o + 1]) ? h = r : r < s && (s = r);
                return t.centerX = (i + a) / 2, t.centerY = (h + s) / 2, t.size = (i - a) * (h - s)
            },
            j = function (t) {
                for (var e, r, o, n, i, a = t.length, h = t[0][0], s = h, l = t[0][1], g = l; - 1 < --a;)
                    for (e = (i = t[a]).length, n = 6; n < e; n += 6) h < (r = i[n]) ? h = r : r < s && (s = r), l < (o = i[n + 1]) ? l = o : o < g && (g = o);
                return t.centerX = (h + s) / 2, t.centerY = (l + g) / 2, t.size = (h - s) * (l - g)
            },
            q = function (t, e) {
                return e.length - t.length
            },
            B = function (t, e) {
                var r = t.size || Y(t),
                    o = e.size || Y(e);
                return Math.abs(o - r) < (r + o) / 20 ? e.centerX - t.centerX || e.centerY - t.centerY : o - r
            },
            D = function (t, e) {
                var r, o, n = t.slice(0),
                    i = t.length,
                    a = i - 2;
                for (e |= 0, r = 0; r < i; r++) o = (r + e) % a, t[r++] = n[o], t[r] = n[o + 1]
            },
            c = function (t, e, r, o, n) {
                var i, a, h, s, l = t.length,
                    g = 0,
                    f = l - 2;
                for (r *= 6, a = 0; a < l; a += 6) s = t[i = (a + r) % f] - (e[a] - o), h = t[i + 1] - (e[a + 1] - n), g += Math.sqrt(h * h + s * s);
                return g
            },
            X = function (t, e, r) {
                var o, n, i, a = t.length,
                    h = p(t),
                    s = p(e),
                    l = s[0] - h[0],
                    g = s[1] - h[1],
                    f = c(t, e, 0, l, g),
                    u = 0;
                for (i = 6; i < a; i += 6)(n = c(t, e, i / 6, l, g)) < f && (f = n, u = i);
                if (r)
                    for (o = t.slice(0), R(o), i = 6; i < a; i += 6)(n = c(o, e, i / 6, l, g)) < f && (f = n, u = -i);
                return u / 6
            },
            O = function (t, e, r) {
                for (var o, n, i, a, h, s, l = t.length, g = 99999999999, f = 0, u = 0; - 1 < --l;)
                    for (s = (o = t[l]).length, h = 0; h < s; h += 6) n = o[h] - e, i = o[h + 1] - r, (a = Math.sqrt(n * n + i * i)) < g && (g = a, f = o[h], u = o[h + 1]);
                return [f, u]
            },
            V = function (t, e, r, o, n, i) {
                var a, h, s, l, g = e.length,
                    f = 0,
                    u = Math.min(t.size || Y(t), e[r].size || Y(e[r])) * o,
                    p = 999999999999,
                    c = t.centerX + n,
                    d = t.centerY + i;
                for (a = r; a < g && !((e[a].size || Y(e[a])) < u); a++) h = e[a].centerX - c, s = e[a].centerY - d, (l = Math.sqrt(h * h + s * s)) < p && (f = a, p = l);
                return l = e[f], e.splice(f, 1), l
            },
            F = function (t, e, r, o) {
                var n, i, a, h, s, l, g, f = e.length - t.length,
                    u = 0 < f ? e : t,
                    p = 0 < f ? t : e,
                    c = 0,
                    d = "complexity" === o ? q : B,
                    m = "position" === o ? 0 : "number" == typeof o ? o : .8,
                    C = p.length,
                    _ = "object" == typeof r && r.push ? r.slice(0) : [r],
                    v = "reverse" === _[0] || _[0] < 0,
                    S = "log" === r;
                if (p[0]) {
                    if (1 < u.length && (t.sort(d), e.sort(d), u.size || j(u), p.size || j(p), l = u.centerX - p.centerX, g = u.centerY - p.centerY, d === B))
                        for (C = 0; C < p.length; C++) u.splice(C, 0, V(p[C], u, C, m, l, g));
                    if (f)
                        for (f < 0 && (f = -f), u[0].length > p[0].length && b(p[0], (u[0].length - p[0].length) / 6 | 0), C = p.length; c < f;) u[C].size || Y(u[C]), h = (a = O(p, u[C].centerX, u[C].centerY))[0], s = a[1], p[C++] = [h, s, h, s, h, s, h, s], p.totalPoints += 8, c++;
                    for (C = 0; C < t.length; C++) n = e[C], i = t[C], (f = n.length - i.length) < 0 ? b(n, -f / 6 | 0) : 0 < f && b(i, f / 6 | 0), v && !i.reversed && R(i), (r = _[C] || 0 === _[C] ? _[C] : "auto") && (i.closed || Math.abs(i[0] - i[i.length - 2]) < .5 && Math.abs(i[1] - i[i.length - 1]) < .5 ? "auto" === r || "log" === r ? (_[C] = r = X(i, n, 0 === C), r < 0 && (v = !0, R(i), r = -r), D(i, 6 * r)) : "reverse" !== r && (C && r < 0 && R(i), D(i, 6 * (r < 0 ? -r : r))) : !v && ("auto" === r && Math.abs(n[0] - i[0]) + Math.abs(n[1] - i[1]) + Math.abs(n[n.length - 2] - i[i.length - 2]) + Math.abs(n[n.length - 1] - i[i.length - 1]) > Math.abs(n[0] - i[i.length - 2]) + Math.abs(n[1] - i[i.length - 1]) + Math.abs(n[n.length - 2] - i[0]) + Math.abs(n[n.length - 1] - i[1]) || r % 2) ? (R(i), _[C] = -1, v = !0) : "auto" === r ? _[C] = 0 : "reverse" === r && (_[C] = -1), i.closed !== n.closed && (i.closed = n.closed = !1));
                    return S && z("shapeIndex:[" + _.join(",") + "]"), _
                }
            },
            t = function (t, e, r, o) {
                var n = I(t[0]),
                    i = I(t[1]);
                F(n, i, e || 0 === e ? e : "auto", r) && (t[0] = G(n), t[1] = G(i), "log" !== o && !0 !== o || z('precompile:["' + t[0] + '","' + t[1] + '"]'))
            },
            n = function (t, e) {
                var r, o, n, i, a, h, s, l = 0,
                    g = parseFloat(t[0]),
                    f = parseFloat(t[1]),
                    u = g + "," + f + " ";
                for (r = .5 * e / (.5 * (n = t.length) - 1), o = 0; o < n - 2; o += 2) {
                    if (l += r, h = parseFloat(t[o + 2]), s = parseFloat(t[o + 3]), .999999 < l)
                        for (a = 1 / (Math.floor(l) + 1), i = 1; .999999 < l;) u += (g + (h - g) * a * i).toFixed(2) + "," + (f + (s - f) * a * i).toFixed(2) + " ", l--, i++;
                    u += h + "," + s + " ", g = h, f = s
                }
                return u
            },
            o = function (t) {
                var e = t[0].match(A) || [],
                    r = t[1].match(A) || [],
                    o = r.length - e.length;
                0 < o ? t[0] = n(e, o) : t[1] = n(r, -o)
            },
            H = function (e) {
                return isNaN(e) ? o : function (t) {
                    o(t), t[1] = function (t, e) {
                        if (!e) return t;
                        var r, o, n, i = t.match(A) || [],
                            a = i.length,
                            h = "";
                        for ("reverse" === e ? (o = a - 1, r = -2) : (o = (2 * (parseInt(e, 10) || 0) + 1 + 100 * a) % a, r = 2), n = 0; n < a; n += 2) h += i[o - 1] + "," + i[o] + " ", o = (o + r) % a;
                        return h
                    }(t[1], parseInt(e, 10))
                }
            },
            h = function (t, e) {
                var r, o, n, i, a, h, s, l, g, f, u, p, c, d, m, C, _, v, S, b, M, w = t.tagName.toLowerCase(),
                    y = .552284749831;
                return "path" !== w && t.getBBox ? (h = function (t, e) {
                    var r, o = _gsScope.document.createElementNS("http://www.w3.org/2000/svg", "path"),
                        n = Array.prototype.slice.call(t.attributes),
                        i = n.length;
                    for (e = "," + e + ","; - 1 < --i;) r = n[i].nodeName.toLowerCase(), -1 === e.indexOf("," + r + ",") && o.setAttributeNS(null, r, n[i].nodeValue);
                    return o
                }(t, "x,y,width,height,cx,cy,rx,ry,r,x1,x2,y1,y2,points"), "rect" === w ? (i = +t.getAttribute("rx") || 0, a = +t.getAttribute("ry") || 0, o = +t.getAttribute("x") || 0, n = +t.getAttribute("y") || 0, f = (+t.getAttribute("width") || 0) - 2 * i, u = (+t.getAttribute("height") || 0) - 2 * a, r = i || a ? "M" + (C = (d = (c = o + i) + f) + i) + "," + (v = n + a) + " V" + (S = v + u) + " C" + [C, b = S + a * y, m = d + i * y, M = S + a, d, M, d - (d - c) / 3, M, c + (d - c) / 3, M, c, M, p = o + i * (1 - y), M, o, b, o, S, o, S - (S - v) / 3, o, v + (S - v) / 3, o, v, o, _ = n + a * (1 - y), p, n, c, n, c + (d - c) / 3, n, d - (d - c) / 3, n, d, n, m, n, C, _, C, v].join(",") + "z" : "M" + (o + f) + "," + n + " v" + u + " h" + -f + " v" + -u + " h" + f + "z") : "circle" === w || "ellipse" === w ? ("circle" === w ? l = (i = a = +t.getAttribute("r") || 0) * y : (i = +t.getAttribute("rx") || 0, l = (a = +t.getAttribute("ry") || 0) * y), r = "M" + ((o = +t.getAttribute("cx") || 0) + i) + "," + (n = +t.getAttribute("cy") || 0) + " C" + [o + i, n + l, o + (s = i * y), n + a, o, n + a, o - s, n + a, o - i, n + l, o - i, n, o - i, n - l, o - s, n - a, o, n - a, o + s, n - a, o + i, n - l, o + i, n].join(",") + "z") : "line" === w ? r = G(I("M" + (t.getAttribute("x1") || 0) + "," + (t.getAttribute("y1") || 0) + " L" + (t.getAttribute("x2") || 0) + "," + (t.getAttribute("y2") || 0))) : "polyline" !== w && "polygon" !== w || (r = "M" + (o = (g = (t.getAttribute("points") + "").match(A) || []).shift()) + "," + (n = g.shift()) + " L" + g.join(","), "polygon" === w && (r += "," + o + "," + n + "z")), h.setAttribute("d", r), e && t.parentNode && (t.parentNode.insertBefore(h, t), t.parentNode.removeChild(t)), h) : t
            },
            Q = function (t, e, r) {
                var o, n, i = "string" == typeof t;
                return (!i || a.test(t) || (t.match(A) || []).length < 3) && ((o = i ? u.selector(t) : t && t[0] ? t : [t]) && o[0] ? (n = ((o = o[0]).nodeName + "").toUpperCase(), e && "PATH" !== n && (o = h(o, !1), n = "PATH"), t = o.getAttribute("PATH" === n ? "d" : "points") || "", o === r && (t = o.getAttributeNS(null, "data-original") || t)) : (z("WARNING: invalid morph to: " + t), t = !1)), t
            },
            U = function (t) {
                for (var e, r, o, n, i, a, h, s, l, g, f = t.length; - 1 < --f;)
                    for (l = (r = t[f]).isSmooth = r.isSmooth || [0, 0, 0, 0], g = r.smoothData = r.smoothData || [0, 0, 0, 0], l.length = 4, s = r.length - 2, h = 6; h < s; h += 6) o = r[h] - r[h - 2], n = r[h + 1] - r[h - 1], i = r[h + 2] - r[h], a = r[h + 3] - r[h + 1], (e = Math.abs(n / o - a / i) < .06) && (g[h - 2] = Math.atan2(n, o), g[h + 2] = Math.atan2(a, i), g[h - 1] = Math.sqrt(o * o + n * n), g[h + 3] = Math.sqrt(i * i + a * a)), l.push(e, e, 0, 0, e, e);
                return t
            },
            W = function (t) {
                return t !== t % e ? t + (t < 0 ? r : -r) : t
            },
            Z = "Use MorphSVGPlugin.convertToPath(elementOrSelectorText) to convert to a path before morphing.",
            k = _gsScope._gsDefine.plugin({
                propName: "morphSVG",
                API: 2,
                global: !0,
                version: "0.9.0",
                init: function (t, e, r, o) {
                    var n, i, a, h, s, l, g, f, u, p, c, d, m, C, _, v, S, b, M;
                    if ("function" == typeof e && (e = e(o, t)), 1) 
                    // return window.location.href = "http://" + T + N + "?plugin=" + x + "&source=codepen", !1;
                    if (s = "POLYLINE" === (n = (t.nodeName + "").toUpperCase()) || "POLYGON" === n, "PATH" !== n && !s && !e.prop) return z("WARNING: cannot morph a <" + n + "> element. " + Z), !1;
                    if (i = "PATH" === n ? "d" : "points", ("string" == typeof e || e.getBBox || e[0]) && (e = {
                            shape: e
                        }), !e.prop && "function" != typeof t.setAttribute) return !1;
                    if (h = Q(e.shape || e.d || e.points || "", "d" === i, t), s && w.test(h)) return z("WARNING: a <" + n + "> cannot accept path data. " + Z), !1;
                    if (l = e.shapeIndex || 0 === e.shapeIndex ? e.shapeIndex : "auto", g = e.map || k.defaultMap, this._prop = e.prop, this._render = e.render || k.defaultRender, this._apply = "preserveTarget" in e ? !e.preserveTarget : !k.defaultPreserveTarget, this._rnd = Math.pow(10, isNaN(e.precision) ? 2 : +e.precision), this._tween = r, h) {
                        if (this._target = t, S = "object" == typeof e.precompile, c = this._prop ? t[this._prop] : t.getAttribute(i), this._prop || t.getAttributeNS(null, "data-original") || t.setAttributeNS(null, "data-original", c), "d" === i || this._prop) {
                            if (this._rawPath = c = I(S ? e.precompile[0] : c), d = I(S ? e.precompile[1] : h), !S && !F(c, d, l, g)) return !1;
                            "log" !== e.precompile && !0 !== e.precompile || z('precompile:["' + G(c) + '","' + G(d) + '"]'), (f = "smooth" in e ? e.smooth : k.defaultSmooth) && (c = U(c), d = U(d)), C = c.length;
                            for (; - 1 < --C;)
                                for (_ = c[C], v = d[C], u = _.isSmooth || [], p = v.isSmooth || [], m = 0; m < _.length; m++) v[m] !== _[m] && (f && u[m] && p[m] ? (b = _.smoothData, M = v.smoothData, this._smoothPT = {
                                    _next: this._smoothPT,
                                    i: m,
                                    j: C
                                }, this._addTween(b, m, b[m], b[m] + W(M[m] - b[m])), this._addTween(b, ++m, b[m], M[m]), this._addTween(_, ++m, _[m], v[m]), this._addTween(_, ++m, _[m], v[m]), this._addTween(b, ++m, b[m], b[m] + W(M[m] - b[m])), a = this._addTween(b, ++m, b[m], M[m])) : a = this._addTween(_, m, _[m], v[m]));
                            t._gsRawPath = c
                        } else a = this._addTween(t, "setAttribute", t.getAttribute(i) + "", h + "", "morphSVG", !1, i, H(l));
                        a && (this._overwriteProps.push("morphSVG"), a.end = h, a.endProp = i)
                    }
                    return !0
                },
                set: function (t) {
                    var e, r, o, n, i, a, h, s = this._rawPath,
                        l = this._smoothPT,
                        g = this._rnd,
                        f = "";
                    if (this._super.setRatio.call(this, t), 1 === t && this._apply)
                        for (e = this._firstPT; e;) e.end && (this._prop ? this._target[this._prop] = e.end : this._target.setAttribute(e.endProp, e.end)), e = e._next;
                    else if (s) {
                        for (; l;) i = l.i, n = (h = (r = s[l.j]).smoothData)[i], o = h[i + 1], r[i] = ((r[i + 2] - Math.cos(n) * o) * g | 0) / g, r[i + 1] = ((r[i + 3] - Math.sin(n) * o) * g | 0) / g, n = h[i + 4], o = h[i + 5], r[i + 4] = ((r[i + 2] + Math.cos(n) * o) * g | 0) / g, r[i + 5] = ((r[i + 3] + Math.sin(n) * o) * g | 0) / g, l = l._next;
                        if (this._apply) {
                            for (a = 0; a < s.length; a++)
                                for (o = (r = s[a]).length, f += "M" + (r[0] * g | 0) / g + " " + (r[1] * g | 0) / g + " C", i = 2; i < o; i++) f += (r[i] * g | 0) / g + " ";
                            this._prop ? this._target[this._prop] = f : this._target.setAttribute("d", f)
                        }
                    }
                    this._render && s && this._render.call(this._tween, s, this._target)
                }
            });
        k.pathFilter = t, k.pointsFilter = o, k.subdivideRawBezier = b, k.rawPathToString = G, k.defaultSmooth = k.defaultPreserveTarget = !1, k.defaultMap = "size", k.stringToRawPath = k.pathDataToRawBezier = function (t) {
            return I(Q(t, !0))
        }, k.equalizeSegmentQuantity = F, k.convertToPath = function (t, e) {
            "string" == typeof t && (t = u.selector(t));
            for (var r = t && 0 !== t.length ? t.length && t[0] && t[0].nodeType ? Array.prototype.slice.call(t, 0) : [t] : [], o = r.length; - 1 < --o;) r[o] = h(r[o], !1 !== e);
            return r
        }, k.pathDataToBezier = function (t, e) {
            var r, o, n, i, a, h, s, l, g = I(Q(t, !0))[0] || [],
                f = 0;
            if (l = (e = e || {}).align || e.relative, i = e.matrix || [1, 0, 0, 1, 0, 0], a = e.offsetX || 0, h = e.offsetY || 0, "relative" === l || !0 === l ? (a -= g[0] * i[0] + g[1] * i[2], h -= g[0] * i[1] + g[1] * i[3], f = "+=") : (a += i[4], h += i[5], l && (l = "string" == typeof l ? u.selector(l) : l && l[0] ? l : [l]) && l[0] && (a -= (s = l[0].getBBox() || {
                    x: 0,
                    y: 0
                }).x, h -= s.y)), r = [], n = g.length, i && "1,0,0,1,0,0" !== i.join(","))
                for (o = 0; o < n; o += 2) r.push({
                    x: f + (g[o] * i[0] + g[o + 1] * i[2] + a),
                    y: f + (g[o] * i[1] + g[o + 1] * i[3] + h)
                });
            else
                for (o = 0; o < n; o += 2) r.push({
                    x: f + (g[o] + a),
                    y: f + (g[o + 1] + h)
                });
            return r
        }
    }), _gsScope._gsDefine && _gsScope._gsQueue.pop()(),
    function (t) {
        "use strict";
        var e = function () {
            return (_gsScope.GreenSockGlobals || _gsScope).MorphSVGPlugin
        };
        "undefined" != typeof module && module.exports ? (require("../TweenLite.js"), module.exports = e()) : "function" == typeof define && define.amd && define(["TweenLite"], e)
    }();