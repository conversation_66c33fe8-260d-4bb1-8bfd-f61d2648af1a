#请根据注释修改，其余内容不修改
apiVersion: apps/v1
kind: Deployment
metadata:
  name: proxng        #[1]必选，Deployment模板名称，可填写服务名称
  namespace: sxaudit-pro
  labels:
    app: proxng       #[2] 必选，标签名，可填写服务名称
spec:
  replicas: 1
  selector:
    matchLabels:
      app: proxng  #[3] 必选，标签名，可填写服务名称
  strategy: {}
  template:
    metadata:
      labels:
        app: proxng     #[4] 必填，pod标签名称，可填写服务名称
    spec:
      containers:
      - image: harbor.dcos.xixian.unicom.local/audit-pro/basenginx:latest   #[6] 镜像名称，请将“supervisionui”替换为服务名，其余不变
        name: proxng        #[5] 必选，容器名称，可填写服务名称
        resources:
          limits:
            memory: 2Gi
status: {}
