import request from '@/utils/request'

// 002获取查询条件字典

export function getQueryInfo() {
  return request({
    url: '/intensive/employee/qualifications/getQueryInfo',
    method: 'post'
  })
}

// 01员工资质信息列表查询

export function selectEmployeeQualificationsList(data, params) {
  return request({
    url: '/intensive/employee/qualifications/selectEmployeeQualificationsList',
    method: 'post',
    data,
    params
  })
}

// 01查询员工资质信息列表:包含保时通

export function selectEmployeeQualificationsListAll(data, params) {
  return request({
    url: '/intensive/employee/qualifications/selectEmployeeQualificationsListAll',
    method: 'post',
    data,
    params
  })
}

// 01查询资质类型字典列表

export function dimQualifications(data, params) {
  return request({
    url: '/intensive/employee/dimQualifications/list',
    method: 'post',
    data,
    params
  })
}

// 04删除资质字典

export function dimQualificationsDelDimInfoById(data) {
  return request({
    url: '/intensive/employee/dimQualifications/delDimInfoById',
    method: 'post',
    data
  })
}

// 02新增、修改保存资质类型字典
export function dimQualificationsSaveDimInfo(data) {
  return request({
    url: '/intensive/employee/dimQualifications/saveDimInfo',
    method: 'post',
    data
  })
}

// 03编辑查询资质字典

export function dimQualificationsGetDimInfoById(data) {
  return request({
    url: '/intensive/employee/dimQualifications/getDimInfoById',
    method: 'post',
    data
  })
}

// 01获取数据主键+字典
export function insertStaffQualificationInfo() {
  return request({
    url: '/intensive/employee/qualifications/insertStaffQualificationInfo',
    method: 'post'
  })
}

// 02批量新增保存选中的资质字典

export function attachmentBatchSaveInfo(data) {
  return request({
    url: '/intensive/employee/qualificationsAttachment/batchSaveInfo',
    method: 'post',
    data
  })
}

// 03根据员工资质表主键查询员工资质附件列表

export function qualificationsAttachmentLits(data) {
  return request({
    url: '/intensive/employee/qualificationsAttachment/list',
    method: 'post',
    data
  })
}

// 06根据主键删除附件业务信息

export function deleteTStaffQualificationsAttachmentById(data) {
  return request({
    url: '/intensive/employee/qualificationsAttachment/deleteTStaffQualificationsAttachmentById',
    method: 'post',
    data
  })
}

// 07保存按钮调用保存方法

export function saveQualificationsInfo(data) {
  return request({
    url: '/intensive/employee/qualifications/saveQualificationsInfo',
    method: 'post',
    data
  })
}

// 04编辑查询员工资质+资质附件列表

export function queryQualiInfoByQualiId(data) {
  return request({
    url: '/intensive/employee/qualifications/queryQualiInfoByQualiId',
    method: 'post',
    data
  })
}

// 05资质证书管理获取字典参数
export function dimQualificationsGetinfo() {
  return request({
    url: '/intensive/employee/dimQualifications/getInfo',
    method: 'post'
  })
}

// 查询公共字典

export function systemDict(type) {
  return request({
    url: '/system/dict/data/type/' + type,
    method: 'get'
  })
}

// 查询附件列表

export function queryAttachmentList(data) {
  return request({
    url: '/sys/attachment/queryAttachmentList',
    method: 'post',
    data: data
  })
}
