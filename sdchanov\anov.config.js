// 打包后配置
;(function() {
  window.__ANOV__PUBLIC__CONFIG = {
    // 设计稿尺寸
    // env: { designSize: { w: 1920, h: 1080 } },
    // dataSource: {
    //   //数据源类型
    //   type: 'file'
    // }
    // voiceFeedback: {
    //   // 语音合成配置
    //   isStart: false,
    //   synthesisType: 'default',
    //   thirdConfig: {
    //     APPID: '5ea14165',
    //     API_SECRET: '4ecfc086936a1efdea4bd07baf7ace4a',
    //     API_KEY: '574a90d9ee6c784558bf23794e8c93cb',
    //     voiceName: 'x2_qige'
    //   }
    // }
    // voiceRecognize: {   //语音识别配置
    //   isStart: false,
    //   APPID: '5ec5d5e9',
    //   API_KEY: '3b539120af4e9d48bbf273640789dc6e',
    //   iatType: 'xf'
    // },
    //   lightSensor: {          //光感识别配置
    //     isStart: false
    //   },
    //   faceRecognize: {        //人脸识别配置
    //     isStart: false
    //   },
    //   gestureRecognize: {     //手势识别配置
    //     isStart: false
    //   },
    //   soundServer: {          //音效管理配置
    //     soundsType: 'default',
    //     isSoundOpen: false
    //   },
    //   animateConfig: {         //动画管理配置
    //     animateDebug: false
    //   },
    //   theme: {                 //主题管理配置
    //     currentThemeValue: 'default'
    //   },
    //   language: {              //国际化配置
    //     lang: 'zh-CN'
    //   },
    // pages: [{}]
  }
})()
