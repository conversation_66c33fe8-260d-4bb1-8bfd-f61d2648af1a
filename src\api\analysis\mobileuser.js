import request from '@/utils/request'
// 经分数据分析-移网用户指标
const BASE_URL = `/analysis/mobileuser`

export class MobileUserSearch {
  /**
   * @description 获取地市列表
   * @params {*}
   * @return {*}
   */
  static getCityList () {
    return request({
      url: `/sys/borough/allNonProvincialCities`,
      method: 'post'
    })
  }

  /**
   * @description 获取数据列表
   * @params {*}
   * @return {*}
   */
  static queryMobileUserList (params) {
    return request({
      url: `${BASE_URL}/queryMobileUserList`,
      method: 'post',
      data: params
    })
  }

}
