user  root;
#
worker_processes  2;

error_log  /var/log/nginx/error.log;
pid        /var/run/nginx.pid;
events {
    worker_connections  10024;
    multi_accept on;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    server_tokens off;

    # 定义日志format
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '$request_time $upstream_addr ';

    # 日志
    access_log  /var/log/nginx/access.log main;

    sendfile       on;
    tcp_nopush     on;

    keepalive_timeout  65;

    #开启gzip压缩
    include gzip.conf;

    fastcgi_intercept_errors off;

    # 配置代理的服务端，请求进来确定使用哪个server
    server {
        listen       8989;
        server_name  127.0.0.1 **************;
        if ($http_Host !~* ^**************|127.0.0.1) {
        		return 403;
        }
        #限制请求体的大小
        client_max_body_size 1000000M;
        #发送request_body的超时时间
        client_body_timeout    3m;
        #发送request_header的超时时间
        client_header_timeout  3m;
        #服务端
        proxy_send_timeout           6000;
        #服务端
        proxy_read_timeout           6000;

        # 代理前段项目
        location / {
            #root /usr/share/nginx/html;

#             set $isValid "0";
#             #文件不存在，为vue路由
#             if (!-e $request_filename) {
#                 set $isValid "${isValid}1";
#             }
#             #不包含多级路径
#             if ($request_uri ~* /\w*/){
#                 set $isValid "${isValid}1";
#             }
#             if ( $isValid  = "011"){
#                 return 404;
#             }

            proxy_pass http://**************:8989/vue;
        }

        location /vue {
            alias   /usr/share/nginx/html/;
            try_files  $uri $uri/ /vue/index.html;
            index  index.html;
        }

        proxy_intercept_errors off;

        # 后台项目
        location /sxaudit {
           include cors.conf;
           include proxy.conf;
           # 使用负载
           proxy_pass   http://**************:8280/sxaudit;
        }

        location /sxpreview {
           include cors.conf;
           include proxy.conf;
           # 使用负载
           proxy_pass   http://**************:8282/sxaudit;
        }

    }

}
