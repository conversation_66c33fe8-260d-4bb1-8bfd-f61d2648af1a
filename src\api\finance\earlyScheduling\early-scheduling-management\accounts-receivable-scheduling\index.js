import request from '@/utils/request'

// 新增获取详情
export function getNewDetailInfo() {
  return request({
    url: '/finance/dispatch/getAddDispatchInfo',
    method: 'post'
  })
}

// 查看、编辑获取详情
export function getDetailInfo(data) {
  return request({
    url: '/finance/dispatch/getDispatchInfo',
    method: 'post',
    data: data
  })
}
// 流程获取详情
export function getProcDetailInfo(data) {
  return request({
    url: '/finance/dispatch/getProcDispatchInfo',
    method: 'post',
    data: data
  })
}

// 已计收项目
export function chargedItemsList(data) {
  return request({
    url: '/finance/receivedDetails/queryReceivedDetailList',
    method: 'post',
    data: data
  })
}

// 未计收预回款项目
export function paymentItemsList(data) {
  return request({
    url: '/finance/uncollectedDetails/queryUncollectedDetailList',
    method: 'post',
    data: data
  })
}

// 已计收项目 删除
export function chargedDelete(data) {
  return request({
    url: '/finance/dispatch/delDispatchDetailById/'+data.id+"/Y",
    method: 'post',
    data: data
  })
}

// 未计收预回款项目 删除
export function paymentDelete(data) {
  return request({
    url: '/finance/dispatch/delDispatchDetailById/'+data.id+"/W",
    method: 'post',
    data: data
  })
}

// 已计收项目选择页
export function collectedList(data,params,formData) {
  return request({
    url: '/finance/receivedDetails/queryReceivedList',
    method: 'post',
    data,
    params,
    formData
  })
}


// 已计收项目保存
export function collectedAdd(data) {
  return request({
    url: '/finance/receivedDetails/insertReceivedDetail/'+data.billNumbers+'/'+data.dispatchId,
    method: 'post'
  })
}

// 未计收预回款项目选择页
export function paymentList(data,params,formData) {
  return request({
    url: '/finance/uncollected/queryUncollectedList',
    method: 'post',
    data,
    params,
    formData
  })
}


// 未计收预回款项目保存
export function paymentAdd(data) {
  return request({
    url: '/finance/uncollectedDetails/insertUncollectedDetail/'+data.ids+'/'+data.dispatchId,
    method: 'post'
  })
}


// 保存
export function dispatchSave(data) {
  return request({
    url: '/finance/dispatch/saveDispReceivableInfo',
    method: 'post',
    data: data
  })
}


// 提交
export function dispatchSubmit(data) {
  return request({
    url: '/finance/dispatch/validateReceivable',
    method: 'post',
    data: data
  })
}

// 新增未计收
export function addPaymentApi(dispatchId,flowDetailType) {
  return request({
    url: '/finance/uncollectedDetails/addUncollectedDetail/'+dispatchId+'/'+flowDetailType,
    method: 'post'
  })
}
//获取流程类型
export function getFlowType(data){
  return request({
    url: '/finance/dispatch/getProcDispatchInfo/'+data.insId,
    method: 'post'
  })
}
//流程推进校验
export function pushProcessValidate(data) {
  return request({
    url: '/finance/dispatch/pushProcessValidate',
    method: 'post',
    data:data
  })
}
  //主流程结束校验
  export function checkProcess(distapchId) {
    return request({
      url: '/finance/dispflow/checkProcess/' + distapchId,
      method: 'post'
    })
  }
    //查询是否存在未结束的催收账款流程
    export function checkSubProcess(distapchId, insId){
      return request({
        url: '/finance/dispflow/checkSubProcess/'+distapchId+'/'+insId,
        method: 'post'
      })
}
