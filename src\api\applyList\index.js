import request from '@/utils/request'

// 01查询申请信息

export function selectResponseApplyInfoList(data, params) {
  return request({
    url: '/intensive/apply/selectResponseApplyInfoList',
    method: 'post',
    data,
    params
  })
}

// /02根据申请表主键删除申请信息、附件及关联表信息

export function delApplyInfoById(data) {
  return request({
    url: '/intensive/apply/delApplyInfoById',
    method: 'post',
    data
  })
}

// 01 授权

export function empowerApplyInfo(data) {
  return request({
    url: '/intensive/apply/empowerApplyInfo',
    method: 'post',
    data
  })
}

// 02通知上传

export function noticeUpload(id) {
  return request({
    url: '/intensive/apply/noticeUpload/' + id,
    method: 'post'
  })
}

// 通知上传发起流程

export function startAndPushProcess(data) {
  return request({
    url: '/intensive/flowable/noticeupload/startAndPushProcess',
    method: 'post',
    data
  })
}
