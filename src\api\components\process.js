import request from '@/utils/request'

// 下一环节名称
export function processLinkData (processDefinitionKey) {
  return request({
    url: '/workflowRestController/tasklinkforstart/' + processDefinitionKey,
    method: 'get'
  })
}

// 首环节发起时加载可选环节
export function tasklinkforstart (processDefinitionKey, data) {
  return request({
    url: '/flowable/flowableRest/tasklinkforstart/' + processDefinitionKey,
    method: 'get',
    params: data
  })
}

// 下一环节处理人
export function refreshNextAssignee (type, data) {
  return request({
    url: type + '/refreshNextAssignee',
    method: 'post',
    data: data
  })
}

// 通过或者退回下一环节名称
export function tasklink (flowTypeKey, linkKey, handleType, data) {
  return request({
    url: '/flowable/flowableRest/tasklink/' + flowTypeKey + '/' + linkKey + '/' + handleType,
    method: 'get',
    params: data
  })
}

// 退回下一环节处理人
export function refreshBackAssignee (data, type = '/flowable/flowableRest') {
  return request({
    url: type + '/refreshBackAssignee',
    method: 'post',
    data: data
  })
}


// 流程启动并推进
export function startAndPushProcess (type, data) {
  return request({
    url: type + '/startAndPushProcess',
    method: 'post',
    data: data
  })
}

// 流程推进
export function pushProcess (type, data) {
  return request({
    url: type + '/pushProcessFree',
    method: 'post',
    data: data
  })
}

// 退回
export function backProcess (data) {
  return request({
    url: '/businessController/backProcess',
    method: 'post',
    data: data
  })
}

// 已办撤回
export function withdrawProcess (data) {
  return request({
    url: '/workflowRestController/withdrawProcess',
    method: 'post',
    data: data
  })
}

// 待阅点击已阅
export function read (readerId) {
  return request({
    url: '/workflowRestController/rebackSendRound',
    method: 'post',
    data: readerId
  })
}

// 根据所在环节查询需展现的自定义标签
export function taburls (processDefinitionId, taskDefinitionKey, tabFlag) {
  return request({
    url: '/workflowRestController/taburls/' + processDefinitionId + '/' + taskDefinitionKey + '/' + tabFlag,
    method: 'get'
  })
}

// 获取已办页面业务主页面及参数
export function taskhasdonepath (data) {
  return request({
    url: '/flowable/flowableRest/taskhasdonepath/' + data.processInstanceId + '/' + data.linkKey + '/' + data.taskId + '/' + data.typeId,
    method: 'get'
  })
}

// 获取待阅、已阅页面业务主页面及参数
export function findRecordPath (data) {
  return request({
    url: '/workflowRestController/findRecordPath',
    method: 'post',
    data: data
  })
}

// 获取流程图地址
export function flowChatData (data) {
  return request({
    url: '/workflowRestController/getProcessChartByProcInstId/' + data.processInstanceId,
    method: 'get'
  })
}

// 获取流转历史
export function histoicflow (processInstanceId) {
  return request({
    url: '/workflowRestController/histoicflow/' + processInstanceId,
    method: 'get'
  })
}

//获取转派人
export function refreshTurnAssignee(data) {
  return request({
    url: '/flowable/flowableRest/refreshTurnAssignee',
    method: 'post',
    data: data
  })
}

// 流程转派
export function turnAssignee(data) {
  return request({
    url: '/flowable/flowTest/turnAssignee',
    method: 'post',
    data: data
  })
}


