import request from '@/utils/request'

// 01 预警调度【月通报】生成数据
export function generateWarningMonthData(data) {
  return request({
    url: '/finance/financeWarningMonth/generateWarningMonthData',
    method: 'post',
    data: data
  })
}

// 06 预警调度【月通报】提交
export function submitWarningMonth(data) {
  return request({
    url: '/finance/financeWarningMonth/submitWarningMonth',
    method: 'post',
    data: data
  })
}

// 05 预警调度【月通报】接收人修改

export function receiverEdit(data) {
  return request({
    url: '/finance/financeWarningMonth/receiverEdit',
    method: 'post',
    data: data
  })
}

// 04 预警调度【月通报】查询接收人列表

export function queryWarningReceiverList(data) {
  return request({
    url: '/finance/financeWarningMonth/queryWarningReceiverList',
    method: 'post',
    data: data
  })
}

// 02 预警调度【月通报】查询通报基本信息

export function queryWarningMonthInfo(data) {
  return request({
    url: '/finance/financeWarningMonth/queryWarningMonthInfo',
    method: 'post',
    data: data
  })
}

// 02 预警调度【月通报】查询通报列表

export function queryWarningMonthDetailList(data) {
  return request({
    url: '/finance/financeWarningMonth/queryWarningMonthDetailList',
    method: 'post',
    data: data
  })
}

