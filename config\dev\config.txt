apiVersion: v1
clusters:
  - cluster:
      insecure-skip-tls-verify: true
      server: https://************:41423;https://************5:43379;https://*************:42953
    name: kubernetes
contexts:
  - context:
      cluster: kubernetes
      user: admin
    name: kubernetes
current-context: kubernetes
kind: Config
preferences: {}
users:
  - name: admin
    user:
      token: 92eeb3f843f7a583439c51763baca5f8