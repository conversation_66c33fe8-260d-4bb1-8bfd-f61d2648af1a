import request from '@/utils/request'

// 查询列表
export function listReport(query) {
  return request({
    url: '/finance/receivable/reportDetails/list',
    method: 'post',
    data: query,
    params: {
      pageNum:query.pageNum,
      pageSize:query.pageSize
    }
  })
}

//09 获取应收账款明细查询
export function getInfo(query) {
  return request({
    url: '/finance/receivable/reportDetails/getInfo/'+query,
    method: 'post'
  })
}

// 10 历史调度信息查询
export function queryHistortInfo(query) {
  return request({
    url: '/finance/receivable/reportDetails/queryHistortInfo/'+query,
    method: 'post'
  })
}

//  11 本次调度信息查询
export function queryThisDispatchDetailInfo(query) {
  return request({
    url: '/finance/receivable/reportDetails/queryThisDispatchDetailInfo/'+query,
    method: 'post'
  })
}

// 12 保存应收账款明细
export function save(data) {
  return request({
    url: '/finance/receivable/reportDetails/save',
    method: 'put',
    data: data
  })
}

// 13 提交应收账款明细
export function validateAndSave(data) {
  return request({
    url: '/finance/receivable/reportDetails/validateAndSave',
    method: 'put',
    data: data
  })
}



