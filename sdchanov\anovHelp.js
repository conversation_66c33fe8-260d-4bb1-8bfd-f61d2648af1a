;(function () {
  function scaleScreen(e, flag) {
    var 
      w = ((__ANOV__PUBLIC__CONFIG &&
        __ANOV__PUBLIC__CONFIG.pageSize &&
        __ANOV__PUBLIC__CONFIG.pageSize.width) ||
        1920)
      h = ((__ANOV__PUBLIC__CONFIG &&
        __ANOV__PUBLIC__CONFIG.pageSize &&
        __ANOV__PUBLIC__CONFIG.pageSize.width) ||
        1080)
    let dw = document.body.clientWidth
    let dh = document.body.clientHeight
      i = Math.min(
        dw / w,
        dh / h
      )
    e.style.width = w + 'px'
    e.style.height = h + 'px'
    e.style.transform = 'scale(' + i + ')'
    e.style.top = (dh - h) / 2 + "px"
    e.style.left = (dw - w) / 2 + "px"
  }
  var dom1 = document.getElementById('appLoadingWrapper')

  scaleScreen(dom1, true)
 
  window.onresize=function(){
    scaleScreen(dom1)
  };
  function resize() {
    let wrap = document.querySelector(this.wrapSelector)
    let view = document.querySelector(this.domSelector)
    if (!(wrap && view)) return
    view.style.position = "absolute"
    view.style.width = this.w + "px"
    view.style.height = this.h + "px"
    let dw = this.wrapSelector ? wrap.clientWidth : document.body.clientWidth
    let dh = this.wrapSelector ? wrap.clientHeight : document.body.clientHeight
    let minRatio = Math.min(dw / this.w, dh / this.h)
    let left, top, right, bottom

    view.style.transform = "scale(" + minRatio + ")"
    top = (dh - this.h) / 2 + "px"
    left = (dw - this.w) / 2 + "px"
  
    top ? (view.style.top = top) : (view.style.bottom = bottom)
    left ? (view.style.left = left) : (view.style.right = right)
  }
  let browserArr = [
    'chrome',
    'firefox',
    'qqbrowser',
    'edg',
    'se 2.x',
    'qihu 360ee',
    'safari'
  ]
  var explorer = window.navigator.userAgent.toLowerCase()
  let showHelp = true
  for (let i = 0; i < browserArr.length; i++) {
    if (explorer.indexOf(browserArr[i]) > -1) {
      showHelp = false
    }
  }
  if (showHelp) {
    help(
      '当前非谷歌、火狐、QQ、360极速、Edge、搜狗浏览器，请切换至以上任一浏览器查看！'
    )
  }
  function help(e) {
    let t =
      `<style>.sys-warn{position:fixed;top:0;left:0;display:flex;justify-content:center;align-items:center;width:100%;height:100%;background:#e73;color:#fff;font-size:26px;padding:0 20%;flex-direction:column;box-sizing:border-box;z-index:120210829}.a-wrap{text-align:right;width:100%}.sys-warn-a{text-decoration:none;color:antiquewhite;font-size:14px;position:relative}.sys-warn-a::after{content:'';display:block;width:100%;height:1px;background:#fff;position:absolute;bottom:-2px;transition:all .3s;transform:scaleX(0);transform-origin:left;left:0}}.sys-warn-a:hover{color:#fff}.sys-warn-a:hover::after{transform:scaleX(1)}</style>` +
      '<div class="sys-warn"><p>' +
      e +
      '</p><div class="a-wrap"><a class="sys-warn-a" href="mailto:<EMAIL>?subject=' +
      encodeURI('anov可视化平台:[' + e + ']') +
      '&body=%E5%A6%82%E6%9E%9C%E5%9B%9E%E5%A4%8D%E4%B8%8D%E5%8F%8A%E6%97%B6%E8%AF%B7%E8%81%94%E7%B3%BB:%E6%9C%B1%E6%B6%A6%E4%BA%9A%2018610279719">[我不明白,我需要帮助]</a></div></div>'
    document.write(t)
  }
  ;/^file/gi.test(window.location.href) && help('请配置web服务器进行浏览！')
})()
