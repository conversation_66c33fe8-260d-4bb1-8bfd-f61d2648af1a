import request from '@/utils/request'

// "编辑控制按钮"

//查询
export function queryEditControllerList(data) {
  return request({
    url: '/system/editcontrol/selectEditControlList',
    method: 'post',
    data
  })
}

//更改开关状态  参数：主键id；开关状态editFlag   0：开；1：关
export function updateEdiFlagById(data) {
  return request({
    url: '/system/editcontrol/editEditControlById',
    method: 'post',
    data
  })
}
