import request from '@/utils/request'
// 经分数据分析-宽带用户指标
const BASE_URL = `/analysis/broadband`

export class BroadbandUserSearch {
  /**
   * @description 获取地市列表
   * @params {*}
   * @return {*}
   */
  static getCityList () {
    return request({
      url: `/sys/borough/allNonProvincialCities`,
      method: 'post'
    })
  }

  /**
   * @description 获取数据列表
   * @params {*}
   * @return {*}
   */
  static queryBroadbandUserIndexList (params) {
    return request({
      url: `${BASE_URL}/queryBroadbandUserIndexList`,
      method: 'post',
      data: params
    })
  }

}
