import request from '@/utils/request'

// 查询财务指标合计
export function queryAnalysisSum(data) {
  return request({
    url: '/analysis/basebusiness/queryAnalysisSum',
    method: 'post',
    data: data
  })
}

// 查询基础业务收入构成图表
export function queryAnalysisBaseECharts(data) {
  return request({
    url: '/analysis/basebusiness/queryAnalysisBaseECharts',
    method: 'post',
    data: data
  })
}
// 3查询移网用户发展质量1图表

export function queryMobileUserECharts(data) {
  return request({
    url: '/analysis/mobileuser/queryMobileUserECharts',
    method: 'post',
    data: data
  })
}

// 移网用户发展质量图表2

export function queryMobileUserQualityECharts(data) {
  return request({
    url: '/analysis/mobileuserquality/queryMobileUserQualityECharts',
    method: 'post',
    data: data
  })
}

// 查询宽带用户发展质量图表

export function queryBroadbandECharts(data) {
  return request({
    url: '/analysis/broadband/queryBroadbandECharts',
    method: 'post',
    data: data
  })
}

// 查询政企业务收入构成图表

export function queryAnalysisEnterpriseECharts(data) {
  return request({
    url: '/analysis/enterprise/queryAnalysisEnterpriseECharts',
    method: 'post',
    data: data
  })
}

// 数据分析-收入分析查询

export function queryAnalysisIncomeData(data) {
  return request({
    url: '/analysis/basebusiness/queryAnalysisIncomeData',
    method: 'post',
    data: data
  })
}

//   7 用户发展质量分析

export function queryAnalysisQualityData(data) {
  return request({
    url: '/analysis/mobileuserquality/queryAnalysisQualityData',
    method: 'post',
    data: data
  })
}

// 查询地市
export function findSdBoroughList() {
  return request({
    url: '/sys/borough/findSdBoroughList',
    method: 'post'
  })
}

/**
 * 获取非省本部地市
 */
export function allNonProvincialCities() {
  return request({
    url: '/sys/borough/allNonProvincialCities',
    method: 'post'
  })
}
