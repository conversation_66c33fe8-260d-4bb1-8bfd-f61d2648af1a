import request from '@/utils/request'

// 查询考核明细列表
export function listAssessment(query,data) {
  return request({
    url: '/finance/areaassessment/input/list',
    method: 'post',
    params: query,
    data: data
  })
}


// 修改考核录入
export function getAssessment(data) {
  return request({
    url: '/finance/areaassessment/input',
    method: 'put',
    data: data
  })
}
// 修改考核录入
export function addAssessment(data) {
  return request({
    url: '/finance/areaassessment/input',
    method: 'put',
    data: data
  })
}
// 修改考核录入
export function updateAssessment(data) {
  return request({
    url: '/finance/areaassessment/input',
    method: 'put',
    data: data
  })
}
// 删除考核录入
export function delAssessment(id) {
  return request({
    url: '/finance/areaassessment/input/' + id,
    method: 'delete'
  })
}



// 删除单个附件
export function dropFileByBusinessId(data) {
  return request({
    url: '/finance/areaassessment/input/dropFileByBusinessId',
    method: 'post',
    data: data
  })
}

// 上报
export function escalation(data) {
  return request({
    url: '/finance/areaassessment/input/escalation',
    method: 'post',
    data: data
  })
}

// 查询统计报表
export function statisticList(query,data) {
  return request({
    url: '/finance/areaassessment/input/statisticList',
    method: 'post',
    params: query,
    data: data
  })
}
// 导出统计报表
export function exportStatisticList(data) {
  return request({
    url: '/finance/areaassessment/input/exportStatisticList',
    method: 'post',
    data: data
  })
}
