import request from '@/utils/request'

const BASE_HEADER_URL = `/score`

export class PointManageAPI {
  /**
   * @description 获取数据列表
   * @param params
   * @param page
   * @return {*}
   */
  static getDataList (params, page) {
    return request({
      url: `${BASE_HEADER_URL}/queryScoreDetailList`,
      method: 'post',
      data: params,
      params: page
    })
  }

  /**
   * @description 获取积分大小项
   * @returns {AxiosPromise}
   */
  static getCondition () {
    return request({
      url: `${BASE_HEADER_URL}/selectCondition`,
      method: 'post',
    })
  }

  /**
   * @description 获取地市数据
   * @returns {AxiosPromise}
   */
  static getProvinceList () {
    return request({
      url: `plan/fill/prefecture`,
      method: 'get',
    })
  }

  /**
   * @description 获取数据列表
   * @param params
   * @param page
   * @return {*}
   */
  static queryAuditResultList (projectId) {
    return request({
      url: `${BASE_HEADER_URL}/queryAuditResultList/${projectId}`,
      method: 'post',
    })
  }

  /**
   * @description 保存参加项目分
   * @param params
   * @return {*}
   */
  static saveProjectMemberScoreList (projectId, params) {
    return request({
      url: `${BASE_HEADER_URL}/saveProjectMemberScoreList/${projectId}`,
      method: 'post',
      data: params
    })
  }
  /**
   * @description 保存参加项目分
   * @param params
   * @return {*}
   */
  static saveProjectMemberScore (projectId, params) {
    return request({
      url: `${BASE_HEADER_URL}/saveProjectMemberScore/${projectId}`,
      method: 'post',
      data: params
    })
  }

  /**
   * @description 保存审计成果分
   * @param projectId
   * @param params
   * @return {*}
   */
  static saveAuditResultList (projectId, params) {
    return request({
      url: `${BASE_HEADER_URL}/saveAuditResultList/${projectId}`,
      method: 'post',
      data: params
    })
  }
  /**
   * @description 保存审计成果分
   * @param projectId
   * @param params
   * @return {*}
   */
  static saveAuditResult (projectId, params) {
    return request({
      url: `${BASE_HEADER_URL}/saveAuditResult/${projectId}`,
      method: 'post',
      data: params
    })
  }

  /**
   * @description 获取项目基本信息
   * @param params
   * @return {*}
   */
  static getProjectStartInfo (params) {
    return request({
      url: `/sub/project/getProjectStartInfo/${params}`,
      method: 'post'
    })
  }

  /**
   * @description 获取数据列表
   * @param params
   * @param page
   * @return {*}
   */
  static queryProjectMemberScoreList (projectId) {
    return request({
      url: `${BASE_HEADER_URL}/queryProjectMemberScoreList/${projectId}`,
      method: 'post',
    })
  }

  /**
   * @description 分配审计成果分，下一环节校验
   * @param params
   * @return {*}
   */
  static checkAuditResultList (projectId) {
    return request({
      url: `${BASE_HEADER_URL}/checkAuditResultList/${projectId}`,
      method: 'post',
    })
  }

  /**
   * @description 组长分配参与项目积分，下一环节校验
   * @param params
   * @return {*}
   */
  static checkAttendProjectScoreList (projectId) {
    return request({
      url: `${BASE_HEADER_URL}/checkAttendProjectScoreList/${projectId}`,
      method: 'post',
    })
  }

}
