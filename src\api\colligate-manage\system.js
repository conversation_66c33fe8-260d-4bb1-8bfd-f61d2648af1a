import request from '@/utils/request'

// 获取案例表主键
export function getSystemId() {
  return request({
    url: '/library/system/getSystemId',
    method: 'post',
  })
}

export function getFileList(params){
  return request({
    url: '/library/system/selectSystemFiles',
    method: 'post',
    params: params
  })
}

export function deleteSystemFile(id){
  return request({
    url: '/library/system/deleteSystemFile/' + id,
    method: 'post',
  })
}

// 查询案例库列表
export function listSystem(params,page) {
  return request({
    url: '/library/system/list',
    method: 'post',
    params: page,
    data:params
  })
}

// 查询案例库详细
export function getSystem(params) {
  return request({
    url: '/library/system/getSystemInfo' ,
    method: 'post',
    data:params
  })
}

/* export function getSuitDepts(query) {
  return request({
    url: '/library/system/getSuitDepts',
    method: 'post',
    params: query
  })
} */

export function saveSystemInfo(param) {
  return request({
    url: '/library/system/saveSystemInfo',
    method: 'post',
    data: param
  })
}

export function releaseSystemInfo(param) {
  return request({
    url: '/library/system/releaseSystemInfo',
    method: 'post',
    data: param
  })
}

export function delSystemInfo(param) {
  return request({
    url: '/library/system/delSystemInfo',
    method: 'post',
    data: param
  })
}
export function updateSystemStatus(param) {
  return request({
    url: '/library/system/updateSystemStatus',
    method: 'post',
    data: param
  })
}





