import request from '@/utils/request'
// 经分数据分析-基础业务收入
const BASE_URL = `/analysis/basebusiness`

export class BaseBusinessSearch {
  /**
   * @description 获取地市列表
   * @params {*}
   * @return {*}
   */
  static getCityList () {
    return request({
      url: `/sys/borough/allNonProvincialCities`,
      method: 'post'
    })
  }

  /**
   * @description 获取数据列表
   * @params {*}
   * @return {*}
   */
  static queryBaseBusinessList (params) {
    return request({
      url: `${BASE_URL}/queryBaseBusinessList`,
      method: 'post',
      data: params
    })
  }

}
