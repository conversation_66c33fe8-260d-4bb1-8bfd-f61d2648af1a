import request from '@/utils/request'

// 查询人才库信息列表
export function listTalnet(query) {
  return request({
    url: '/analysis/talnet/list',
    method: 'get',
    params: query
  })
}

// 查询人才库信息详细
export function getTalnet(id) {
  return request({
    url: '/analysis/talnet/' + id,
    method: 'get'
  })
}

// 新增人才库信息
export function addTalnet(data) {
  return request({
    url: '/analysis/talnet',
    method: 'post',
    data: data
  })
}

// 修改人才库信息
export function updateTalnet(data) {
  return request({
    url: '/analysis/talnet',
    method: 'put',
    data: data
  })
}

// 删除人才库信息
export function delTalnet(id) {
  return request({
    url: '/analysis/talnet/' + id,
    method: 'delete'
  })
}

// 导出人才库信息
export function exportTalnet(query) {
  return request({
    url: '/analysis/talnet/export',
    method: 'get',
    params: query
  })
}