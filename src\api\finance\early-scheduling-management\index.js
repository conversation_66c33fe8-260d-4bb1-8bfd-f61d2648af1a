import request from '@/utils/request'

// 查询考核明细列表
export function queryWarningList(data, query) {
  return request({
    url: '/finance/financeWarningManage/queryWarningList',
    method: 'post',
    params: query,
    data: data
  })
}
// 删除调度
export function delReceiveInfoById(data) {
  return request({
    url: '/finance/dispatch/delDispatchInfoById',
    method: 'post',
    data: data
  })
}

// 07 预警调度【月通报】删除

export function dropWarningMonth(data) {
  return request({
    url: '/finance/financeWarningMonth/dropWarningMonth',
    method: 'post',
    data: data
  })
}

/**
 * 删除扣收记录
 * @param id
 */
export function deleteWithholdingInfo(id) {
  return request({
    url: '/finance/financeWithholding/deleteWithholdingInfo/' + id,
    method: 'post'
  });
}
