user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;
events {
    worker_connections  1024;
}

stream{
    log_format proxy '$remote_addr [$time_local]'
     '$session_time "$upstream_addr" '
               '"$upstream_bytes_sent" "$upstream_bytes_received" "$upstream_connect_time"';
    access_log /var/log/nginx/access.log proxy ;
    error_log /var/log/nginx/error.log;
    open_log_file_cache off;

    server {
        listen 8989;
        proxy_connect_timeout 10s;
        proxy_timeout 3000s;
        proxy_pass 10.170.27.19:15332;
    }

    server {
        listen 8888;
        proxy_connect_timeout 10s;
        proxy_timeout 3000s;
        proxy_pass 10.170.27.19:12345;
    }
     server {
        listen 9000;
        proxy_connect_timeout 10s;
        proxy_timeout 3000s;
        proxy_pass 10.170.27.17:10000;
    }

    server {
        listen 9001;
        proxy_connect_timeout 10s;
        proxy_timeout 3000s;
        proxy_pass 10.129.89.161:5230;
    }
}
