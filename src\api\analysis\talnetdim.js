import request from '@/utils/request'

// 查询人才类型字典列表
export function listTalnetdim(params,query) {
  return request({
    url: '/analysis/talnetdim/list',
    method: 'post',
    data: query,
    params: params
  })
}

// 查询人才类型字典详细
export function getTalnetdim(id) {
  return request({
    url: '/analysis/talnetdim/getTalentDimDetailById',
    method: 'post',
    data: {"id":id}
  })
}

// 新增人才类型字典
export function addTalnetdim(data) {
  return request({
    url: '/analysis/talnetdim/addTalentInfo',
    method: 'post',
    data: data
  })
}

// 修改人才类型字典
export function updateTalnetdim(data) {
  return request({
    url: '/analysis/talnetdim/editTalentInfo',
    method: 'post',
    data: data
  })
}

// 删除人才类型字典
export function delTalnetdim(id) {
  return request({
    url: '/analysis/talnetdim/dropTalentById',
    method: 'post',
    data: {"id":id}
  })
}

// 导出人才类型字典
export function exportTalnetdim(query) {
  return request({
    url: '/analysis/talnetdim/export',
    method: 'post',
    data: query
  })
}
