import request from '@/utils/request'

const BASE_HEADER_URL = `/forumTheme`

export class ForumAPI {
  /**
   * @description 获取论坛数据列表
   * @param params
   * @return {*}
   */
  static getDataCount (params) {
    return request({
      url: `${BASE_HEADER_URL}/findCountProblems`,
      method: 'post',
      data: params
    })
  }

  /**
   * @description 获取论坛数据列表
   * @param params
   * @param page
   * @return {*}
   */
  static getDataList (params, page) {
    return request({
      url: `${BASE_HEADER_URL}/findProblems`,
      method: 'post',
      data: params,
      params: page
    })
  }

  /**
   * @description 回复
   * @param params
   * @param page
   * @return {*}
   */
  static replyTheme (params, page) {
    return request({
      url: `${BASE_HEADER_URL}/replyTheme`,
      method: 'post',
      data: params,
      params: page
    })
  }

  /**
   * @description 获取论坛数据详情
   * @param params
   * @return {*}
   */
  static getDetails (params) {
    return request({
      url: `${BASE_HEADER_URL}/findProblemDetails/${params}`,
      method: 'post'
    })
  }

  /**
   * @description 获取一级回复列表
   * @param params
   * @return {*}
   */
  static getFirstRecoveryList (params) {
    return request({
      url: `${BASE_HEADER_URL}/findReplyMajorDetails/${params}`,
      method: 'post'
    })
  }

  /**
   * @description 获取二级回复列表
   * @param params
   * @return {*}
   */
  static getSecondRecoveryList (params) {
    return request({
      url: `${BASE_HEADER_URL}/findReplyMinorDetails/${params}`,
      method: 'post'
    })
  }

  /**
   * @description 点赞
   * @param params
   * @return {*}
   */
  static praise (params) {
    return request({
      url: `${BASE_HEADER_URL}/likeTheTheme/${params}`,
      method: 'post'
    })
  }

  /**
   * @description 关注
   * @param params
   * @return {*}
   */
  static follow (params) {
    return request({
      url: `${BASE_HEADER_URL}/concernTheTheme/${params}`,
      method: 'post'
    })
  }

  /**
   * @description 采纳
   * @param params
   * @return {*}
   */
  static accept (params) {
    return request({
      url: `${BASE_HEADER_URL}/acceptReply/${params}`,
      method: 'post'
    })
  }

  /**
   * @description 获取邀请回答人员
   * @param params
   * @param page
   * @return {*}
   */
  static getInviteList (params, page) {
    return request({
      url: `${BASE_HEADER_URL}/queryUsers`,
      method: 'post',
      data: params,
      params: page
    })
  }

  /**
   * @description 我要提问提交
   * @param params
   * @return {*}
   */
  static initialization (params) {
    return request({
      url: `${BASE_HEADER_URL}/initialization`,
      method: 'post',
      data: params,
    })
  }

  /**
   * @description 我要提问 文件上传
   * @param params
   * @return {*}
   */
  static uploadFile (params) {
    return request({
      url: `${BASE_HEADER_URL}/uploadFile`,
      method: 'post',
      data: params,
    })
  }

  /**
   * @description 我要提问 文件获取
   * @param params
   * @return {*}
   */
  static getFileList (params) {
    return request({
      url: `${BASE_HEADER_URL}/queryFile`,
      method: 'post',
      data: params,
    })
  }

  /**
   * @description 我要提问 文件删除
   * @param id
   * @return {*}
   */
  static deleteFile (id) {
    return request({
      url: `${BASE_HEADER_URL}/deleteFile/${id}`,
      method: 'post'
    })
  }

  /**
   * @description 我要提问提交
   * @param params
   * @return {*}
   */
  static submit (params) {
    return request({
      url: `${BASE_HEADER_URL}/raiseProblem`,
      method: 'post',
      data: params,
    })
  }
}
