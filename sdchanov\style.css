html,
body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin: 0;
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
    'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial,
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
  font-variant: tabular-nums;
  line-height: 1.5;
}
p {
  margin: 0;
  padding: 0;
}
#appLoading {
  width: 100%;
  height: 100%;
  background: #000;
  position: fixed;
  z-index: 20210906;
}
#appLoadingWrapper{
  position: absolute;
}
#appLoadingInfo {
  position: relative;
  width: 300px;
  height: 46px;
  top: 60px;
  left: 60px;
}
.appLoadingInfoLine {
  position: absolute;
  width: 2px;
  height: 100%;
  background-image: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.45) 50%,
    rgba(255, 255, 255, 0.3) 50%
  );
  background-size: 100%;
  transition: all 0.3s ease-in-out;
  animation: lineAni 0.3s;
}
@keyframes lineAni {
  0% {
    transform: scaleY(0);
  }
  100% {
    transform: scaleY(1);
  }
}
#projectName {
  position: absolute;
  left: 16px;
  color: rgba(255, 255, 255, 0.4);
  font-size: 18px;
  line-height: 24px;
  animation: nameAni 0.5s ease-in-out;
}
@keyframes nameAni {
  0% {
    opacity: 0;
    top: -20px;
  }
  100% {
    opacity: 1;
    top: 0;
  }
}
#copyright {
  position: absolute;
  left: 16px;
  bottom: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.2);
  animation: copyrightAni 0.5s ease-in-out;
}
@keyframes copyrightAni {
  0% {
    opacity: 0;
    bottom: -20px;
  }
  100% {
    opacity: 1;
    bottom: 0;
  }
}
#loading-wrapper {
  position: absolute;
  width: 550px;
  height: 450px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
  left: 50%;
  top: 50%;
  margin-top: -300px;
  margin-left: -225px;
}

.loading-gif {
  width: 320px;
  height: 320px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: url('./img/sys-img/loading.webp') no-repeat;
  animation: loadingGif 0.5s;
  position: relative;
}
.loading-gif::after {
  position: absolute;
  display: block;
  content: 'DEV 2.0';
  font-weight: 700;
  color: rgba(255, 255, 255, 0.15);
  bottom: 98px;
  right: 72px;
  font-size: 18px;
  mix-blend-mode: color-dodge;
}
.loading-gif i {
  margin-top: 22px;
  font-size: 28px;
  color: rgba(255, 255, 255, 0.2);
}
@keyframes loadingGif {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
#loading-progress {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: start;
  margin-top: -10px;
}
.loading-progress-top-line {
  width: 24px;
  height: 1px;
  background: #20e7ff;
  animation: progressLine 0.3s;
}
@keyframes progressLine {
  0% {
    width: 0;
  }
  100% {
    width: 40px;
  }
}
.loading-loading {
  margin: 0;
  font-size: 12px;
  color: #20e7ff;
  font-family: 'acens';
  line-height: 24px;
  animation: progressLoading 1s infinite;
}
@keyframes progressLoading {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.loading-progress-bar {
  position: relative;
  height: 1px;
  margin-top: 4px;
  background: rgba(240, 240, 240, 0.15);
  border-radius: 3px;
  /* animation: barAni .5s ease-in-out; */
}
/* @keyframes barAni {
    0% {width: 0}
    100% {width: 100%}
} */
.loading-progress-bar-ani {
  position: absolute;
  width: 2%;
  height: 100%;
  background: linear-gradient(to right, #207fff, #20e7ff);
  border-radius: 3px;
}
.loading-progress-content {
  position: relative;
  height: 20px;
  line-height: 20px;
  margin-top: 6px;
  animation: loadingContentBg 0.2s;
}
@keyframes loadingContentBg {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.loading-progress-text {
  font-size: 12px;
  font-family: 'HYQiHeiY2-35J';
  color: rgba(203, 237, 248, 0.5);
}
.loading-progress-num {
  position: absolute;
  right: 0;
  color: #20e7ff;
  font-size: 12px;
  font-family: 'acens';
}
.loading-warning {
  position: absolute;
  right: 42px;
  right: 0;
  top: 42px;
  width: 305px;
  height: 124px;
  color: #fbc255;
  background: url('./img/sys-img/loading-warning-yellow.png') no-repeat;
  opacity: 0;
}

.loading-colse-btn {
  position: absolute;
  right: 0px;
  top: 0px;
  width: 40px;
  height: 20px;
  font-size: 12px;
  line-height: 20px;
  text-align: center;
  cursor: pointer;
}
.loading-warn-text {
  position: absolute;
  right: 10px;
  top: 32px;
  width: 190px;
  height: 80px;
  font-size: 12px;
  color: #fbc255;
}
.laoding-ani {
  position: absolute;
  width: 60px;
  height: 60px;
  top: 32px;
  left: 20px;
  background: url('./img/sys-img/loading-yellow.webp') no-repeat;
  background-size: 100% 100%;
}
.loading-warn-text:hover {
  text-decoration: underline;
}
.loading-warning.bad {
  color: #f8463d;
  background: url('./img/sys-img/loading-warning-red.png') no-repeat;
}
.loading-warning.bad .loading-warn-text {
  color: #f8463d;
}
.loading-warning.bad .laoding-ani {
  background: url('./img/sys-img/loading-red.webp') no-repeat;
  background-size: 100% 100%;
}
@font-face {
  font-family: 'DS-DIGI';
  src: url('./fonts/DS-DIGI.TTF') format('truetype');
}
@font-face {
  font-family: 'acens';
  src: url('./fonts/acens.ttf') format('truetype');
}
.acens {
  font-family: "acens" !important;
  font-size: 14px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.DS-DIGI {
  font-family: "DS-DIGI" !important;
  font-size: 14px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
