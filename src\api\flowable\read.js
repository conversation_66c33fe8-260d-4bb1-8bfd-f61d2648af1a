import request from '@/utils/request'


  // 翻页查找待阅任务列表（待阅列表）
  export function taskToRead(param,data) {
    return request({
      url: '/flowable/flowableRest/taskToRead',
      method: 'post',
      data: data,
      params: param
    })
}

// 翻页查找已阅任务列表（已阅列表）
export function taskHasRead(param,data) {
  return request({
    url: '/flowable/flowableRest/taskHasRead',
    method: 'post',
    data: data,
    params: param
  })
}

// 待阅点击已阅
export function read(data) {
  return request({
    url: '/flowable/flowableRest/rebackSendRound',
    method: 'post',
    data: data
  })
}

// 获取待阅、已阅页面业务主页面及参数
export function findRecordPath(readId) {
  return request({
    url: '/flowable/flowableRest/findRecordPath/'+readId,
    method: 'get'
  })
}

