import request from '@/utils/request'

// "01查询人员信息查询列表"

export function systemDictData(data, params) {
  return request({
    url: '/analysis/personmark/list',
    method: 'post',
    data,
    params
  })
}

// "02导出人员信息查询列表"

export function exportList(data, params) {
  return request({
    url: '/analysis/personmark/exportList',
    method: 'post',
    data,
    params
  })
}

// 更具角色查询人员

export function personmarklistAll(data, params) {
  return request({
    url: '/analysis/personmark/listAll',
    method: 'post',
    data,
    params
  })
}

// 获取员工履历业务能力信息详细信息

export function queryStaffResumeById(data) {
  return request({
    url: '/analysis/staffmark/queryStaffResumeById',
    method: 'post',
    data
  })
}

// 12 修改员工履历业务能力信息

export function editStaffResumeById(data) {
  return request({
    url: '/analysis/staffmark/editStaffResumeById',
    method: 'post',
    data
  })
}

// 09 查询员工履历业务能力信息列表

export function queryStaffResumeByStaffId(data) {
  return request({
    url: '/analysis/staffmark/queryStaffResumeByStaffId',
    method: 'post',
    data
  })
}

// 06 获取员工从事项目信息详细信息
export function queryStaffProjById(data) {
  return request({
    url: '/analysis/staffmark/queryStaffProjById',
    method: 'post',
    data
  })
}

// 新增员工履历业务能力信息

export function saveStaffResumeById(data) {
  return request({
    url: '/analysis/staffmark/saveStaffResumeById',
    method: 'post',
    data
  })
}

//  修改员工从事项目信息

export function editStaffProjById(data) {
  return request({
    url: '/analysis/staffmark/editStaffProjById',
    method: 'post',
    data
  })
}

// 07 新增员工从事项目信息

export function saveStaffProjById(data) {
  return request({
    url: '/analysis/staffmark/saveStaffProjById',
    method: 'post',
    data
  })
}

// "01查询人员列表"

export function staffmarkList(data, params) {
  return request({
    url: '/analysis/staffmark/list',
    method: 'post',
    data,
    params
  })
}
// 05 查询员工从事项目信息列表--本公司-参与项目
export function queryInProjByUserName(data) {
  return request({
    url: '/analysis/staffmark/selectStaffProjectInList',
    method: 'post',
    data
  })
}
// 05 查询员工从事项目信息列表--本公司-参与项目实施
export function queryPlatformProjByUserName(data) {
  return request({
    url: '/analysis/staffmark/queryPlatformProjectByUserName',
    method: 'post',
    data
  })
}
// 05 查询员工从事项目信息列表--其他公司从事项目
export function queryStaffProjByStaffId(data) {
  return request({
    url: '/analysis/staffmark/selectStaffProjectOutList',
    method: 'post',
    data
  })
}

// 04修改人员信息打标

export function saveStaffInfoById(data) {
  return request({
    url: '/analysis/staffmark/saveStaffInfoById',
    method: 'post',
    data
  })
}

// "03 获取人员打标详细信息"

export function getStaffDetailById(data) {
  return request({
    url: '/analysis/staffmark/getStaffDetailById',
    method: 'post',
    data
  })
}

// 更新状态为编写完成
export function validateAndSubmitStaffInfoById(data) {
  return request({
    url: '/analysis/staffmark/validateAndSubmitStaffInfoById',
    method: 'post',
    data
  })
}

// 更新状态为已完成
export function validateAndCompleteStaffInfoById(data) {
  return request({
    url: '/analysis/staffmark/validateAndCompleteStaffInfoById',
    method: 'post',
    data
  })
}

// 本人填报流程 校验
export function validateSubmitStaffInfoById(data) {
  return request({
    url: '/analysis/staffmark/validateSubmitStaffInfoById',
    method: 'post',
    data
  })
}
// 9 删除员工从事项目信息

export function dropStaffProjById(data) {
  return request({
    url: '/analysis/staffmark/dropStaffProjById',
    method: 'post',
    data
  })
}

// 14 删除员工履历业务能力信息

export function dropStaffResumeById(data) {
  return request({
    url: '/analysis/staffmark/dropStaffResumeById',
    method: 'post',
    data
  })
}
