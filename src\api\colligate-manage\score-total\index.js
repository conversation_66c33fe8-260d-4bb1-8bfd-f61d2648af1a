import request from '@/utils/request'

const BASE_HEADER_URL = `/score`

export class ScoreTotalAPI {
  /**
   * @description 获取数据列表
   * @param params
   * @param page
   * @return {*}
   */
  static getDataList(params, page) {
    return request({
      url: `${BASE_HEADER_URL}/queryScoreTotalList`,
      method: 'post',
      data: params,
      params: page
    })
  }

  /**
   * @description 获取积分大小项
   * @returns {AxiosPromise}
   */
  static getCondition() {
    return request({
      url: `${BASE_HEADER_URL}/selectCondition`,
      method: 'post',
    })
  }

  /**
   * @description 获取地市数据
   * @returns {AxiosPromise}
   */
  static getProvinceList() {
    return request({
      url: `plan/fill/prefecture`,
      method: 'get',
    })
  }
}
