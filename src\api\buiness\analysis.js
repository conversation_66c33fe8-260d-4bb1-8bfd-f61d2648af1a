import request from '@/utils/request'

// 字典获取

export function systemDictData(query) {
  return request({
    url: '/system/dict/data/type/' + query,
    method: 'get'
  })
}

// 01查询合同打标列表"
export function contractmarkList(query, params) {
  return request({
    url: '/analysis/contractmark/list',
    method: 'post',
    data: query,
    params: params

  })
}
// 查询合同信息

export function queryContractInfo(query) {
  return request({
    url: '/analysis/contractmark/queryContractInfo',
    method: 'post',
    data: query
  })
}

// 查询合同打标信息

export function queryContractMarkInfo(query) {
  return request({
    url: '/analysis/contractmark/queryContractMarkInfo',
    method: 'post',
    data: query
  })
}

// "05 查询所属省份：全国省分+总部"

export function queryProvList() {
  return request({
    url: '/analysis/contractmark/queryProvList',
    method: 'post'
  })
}

// "查询所属地市：地市信息"

export function queryAreaList(query) {
  return request({
    url: '/analysis/contractmark/queryAreaList',
    method: 'post',
    data: query
  })
}

// "07 保存合同打标信息"

export function contractmarkSave(data) {
  return request({
    url: '/analysis/contractmark/save',
    method: 'put',
    data: data
  })
}

// 查询项目打标列表"
export function projectmarkList(query, params) {
  return request({
    url: '/analysis/projectmark/list',
    method: 'post',
    data: query,
    params: params

  })
}

// "02导出项目打标列表"

export function projectmarkExportList(query, params) {
  return request({
    url: '/analysis/projectmark/exportList',
    method: 'post',
    data: query,
    params: params

  })
}

// "06 查询产互项目经理列表"

export function queryManagerList(query) {
  return request({
    url: '/analysis/projectmark/queryManagerList/' + query.id,
    method: 'post'

  })
}

// 查询项目信息"

export function queryProjectInfo(query) {
  return request({
    url: '/analysis/projectmark/queryProjectInfo',
    method: 'post',
    data: query
  })
}

export function queryProjectMarkInfo(query) {
  return request({
    url: '/analysis/projectmark/queryProjectMarkInfo',
    method: 'post',
    data: query
  })
}

// 查询产互部门下拉
export function queryDepartmentInfo(query) {
  return request({
    url: '/analysis/projectmark/queryDepartmentInfo',
    method: 'post'
  })
}

// "08 查询山东17地市+本部"

export function querySdAreaList(data) {
  return request({
    url: '/analysis/projectmark/querySdAreaList',
    method: 'post',
    data: data
  })
}
// "05 查询当前项目关联自研产品列表"

export function queryRelationSelfProductInfo(query) {
  return request({
    url: '/analysis/projectmark/queryRelationSelfProductInfo',
    method: 'post',
    data: query
  })
}

// 09 查询自研产品列表，剔除掉已关联本项目的自研产品"

export function queryAllSelfProductInfo(query) {
  return request({
    url: '/analysis/projectmark/queryAllSelfProductInfo',
    method: 'post',
    data: query
  })
}

// 0 保存自研产品"

export function saveSelfProductInfo(query) {
  return request({
    url: '/analysis/projectmark/saveSelfProductInfo',
    method: 'post',
    data: query
  })
}
// 删除自研产品"

export function dropSelfProductInfo(query) {
  return request({
    url: '/analysis/projectmark/dropSelfProductInfo',
    method: 'post',
    data: query
  })
}

// 12 更新项目自研产品的金额"

export function updateRelationSelfProductByProductId(query) {
  return request({
    url: '/analysis/projectmark/updateRelationSelfProductByProductId',
    method: 'post',
    data: query
  })
}

// "13 保存项目打标信息"

export function projectmarkSave(query) {
  return request({
    url: '/analysis/projectmark/save',
    method: 'put',
    data: query
  })
}

//"14 提交项目打标信息"
export function projectmarkSubmit(query) {
  return request({
    url: '/analysis/projectmark/submitProjectMarkInfo',
    method: 'put',
    data: query
  })
}

// 查询重点产品、所属部门"
export function queryProductAndDept() {
  return request({
    url: '/analysis/projectmark/queryProductAndDept',
    method: 'get'
  })
}

// "14 查询相关人员列表"
export function queryProjectPersonList(data) {
  return request({
    url: '/analysis/projectmark/queryProjectPersonList',
    method: 'post',
    data: data
  })
}

// "15 修改相关人员信息"
export function updateProjectPerson(data) {
  return request({
    url: '/analysis/projectmark/updateProjectPerson',
    method: 'post',
    data: data
  })
}

// 查询关联项目信息
export function selectContractProjinfoList(data) {
  return request({
    url: '/analysis/contractmark/selectContractProjinfoList',
    method: 'post',
    data: data
  })
}

// 查询未关联项目"
export function selectNounProjectList(data, params) {
  return request({
    url: '/analysis/contractmark/selectNounProjectList',
    method: 'post',
    data: data,
    params: params
  })
}

// 删除关联项目
export function deleteContractProject(id) {
  return request({
    url: '/analysis/contractmark/deleteContractProject/' + id,
    method: 'post'
  })
}

// "14 保存关联项目"
export function saveContractProject(data) {
  return request({
    url: '/analysis/contractmark/saveContractProject',
    method: 'put',
    data: data
  })
}
export function queryProjectPlatformPersonList(data) {
  return request({
    url: '/analysis/projectmark/queryProjectPlatformPersonList',
    method: 'post',
    data: data
  })
}
export function updatePlatformPersonTaskTime(data) {
  return request({
    url: '/analysis/projectmark/savePlatformPersonTime',
    method: 'put',
    data: data
  })
}

// 11 查询合同多税率"
export function selectContractTaxrateList(data) {
  return request({
    url: '/analysis/contractmark/selectContractTaxrateList',
    method: 'post',
    data: data
  })
}

// "13 保存合同多税率"

export function saveContractTaxrate(data) {
  return request({
    url: '/analysis/contractmark/saveContractTaxrate',
    method: 'put',
    data: data
  })
}

// "12 删除合同多税率"

export function deleteContractTaxrate(id) {
  return request({
    url: '/analysis/contractmark/deleteContractTaxrate/' + id,
    method: 'post'
  })
}

// "14 应收账款明细菜单列表查询"

export function selectContractPayinfoList(data) {
  return request({
    url: '/analysis/contractmark/selectContractPayinfoList',
    method: 'post',
    data: data
  })
}

// "18 保存约定收款信息"

export function seveContractPayinfo(data) {
  return request({
    url: '/analysis/contractmark/seveContractPayinfo',
    method: 'post',
    data: data
  })
}

// "17 删除约定收款信息"

export function deleteContractPayinfo(id) {
  return request({
    url: '/analysis/contractmark/deleteContractPayinfo/' + id,
    method: 'post'
  })
}

// "15 查询问题关键字"

export function queryContractKeyword() {
  return request({
    url: '/analysis/contractmark/queryContractKeyword',
    method: 'get'
  })
}

// 19 查询附件列表"

export function queryContractAttachmentList(data) {
  return request({
    url: '/analysis/contractmark/queryContractAttachmentList',
    method: 'post',
    data: data
  })
}

// 删除附件

export function deleteContractAttachment(id) {
  return request({
    url: '/analysis/contractmark/deleteContractAttachment/' + id,
    method: 'post'
  })
}

// "01查询合同案例列表"

export function analysiscontractList(data, params) {
  return request({
    url: '/analysis/contract/list',
    method: 'post',
    data,
    params: params
  })
}

// 新增项目双计信息

export function addDoubleInfo(data) {
  return request({
    url: '/analysis/projectmark/double/addDoubleInfo',
    method: 'post',
    data: data
  })
}

// 修改项目双计信息
export function projectmarkDouble(data) {
  return request({
    url: '/analysis/projectmark/double/editDoubleInfo',
    method: 'post',
    data: data
  })
}

// 删除项目双计信息

export function projectmarkDoubleDelete(data) {
  return request({
    url: '/analysis/projectmark/double/dropDoubleInfo',
    method: 'post',
    data
  })
}

// "01 查询项目双计列表"

export function projectDoubleMarkList(data) {
  return request({
    url: '/analysis/projectmark/double/list',
    method: 'post',
    data: data
  })
}

// "02 获取项目双计信息详细信息"

export function projectmarkGetInfoById(data) {
  return request({
    url: '/analysis/projectmark/double/getInfoById',
    method: 'post',
    data: data
  })
}
