import request from '@/utils/request'

// 预警列表详情
export function pageList(params, monthId) {
  return request({
    url: '/finance/earlyWarning/selectOverdueList/' + monthId,
    method: 'post',
    params: params
  })
}

// 催收列表详情
export function selectCollectionList(params, monthId) {
  return request({
    url: '/finance/earlyWarning/selectCollectionList/' + monthId,
    method: 'post',
    params: params
  })
}

// 即将逾期预警通知公示

export function selectNoticeOverdueList(params, monthId) {
  return request({
    url: '/finance/earlyWarning/selectNoticeOverdueList/' + monthId,
    method: 'post',
    params: params
  })
}

// 催收提醒详情通知公示

export function selectNoticeCollectionList(params, monthId) {
  return request({
    url: '/finance/earlyWarning/selectNoticeCollectionList/' + monthId,
    method: 'post',
    params: params
  })
}
