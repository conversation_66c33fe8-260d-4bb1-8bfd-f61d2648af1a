import request from '@/utils/request'
// 经分数据分析-政企业务收入
const BASE_URL = `/analysis/enterprise`

export class EnterpriseSearch {
  /**
   * @description 获取地市列表
   * @params {*}
   * @return {*}
   */
  static getCityList () {
    return request({
      url: `/sys/borough/allNonProvincialCities`,
      method: 'post'
    })
  }

  /**
   * @description 获取数据列表
   * @params {*}
   * @return {*}
   */
  static queryEnterpriseList (params) {
    return request({
      url: `${BASE_URL}/queryEnterpriseList`,
      method: 'post',
      data: params
    })
  }

}
