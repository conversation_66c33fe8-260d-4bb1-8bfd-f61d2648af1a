import request from '@/utils/request'

// 01查询申请信息

export function queryApplyInfo(data) {
  return request({
    url: '/intensive/apply/queryApplyInfo',
    method: 'post',
    data
  })
}

// 09申请中查询纳税社保信息列表

export function selectApplyTaxInfoList(data) {
  return request({
    url: '/intensive/social/selectApplyTaxInfoList',
    method: 'post',
    data
  })
}

//   02保存资质申请记录

export function updateResponseApplyInfo(data) {
  return request({
    url: '/intensive/apply/updateResponseApplyInfo',
    method: 'post',
    data
  })
}

// 03提交资质申请记录

export function submitResponseApplyInfo(data) {
  return request({
    url: '/intensive/apply/submitResponseApplyInfo',
    method: 'post',
    data
  })
}

// 04上传申请依据

export function uploadApplyAttachment(data) {
  return request({
    url: '/intensive/applyAttachment/uploadApplyAttachment',
    method: 'post',
    data
  })
}

// 05根据申请依据附件表主键删除附件

export function delApplyAttachment(applyAttachmentId) {
  return request({
    url: '/intensive/applyAttachment/delApplyAttachment/' + applyAttachmentId,
    method: 'post'
  })
}

// 07添加申请

export function addApplyInfo(data) {
  return request({
    url: '/intensive/apply/addApplyInfo',
    method: 'post',
    data
  })
}

// 06删除某个申请

export function delApplyContentById(applyAttachmentId) {
  return request({
    url: '/intensive/apply/delApplyContentById/' + applyAttachmentId,
    method: 'post'
  })
}

// 08 新增个人纳税社保信息

export function addApplyAddSocial(data) {
  return request({
    url: '/intensive/social/addApplyAddSocial',
    method: 'post',
    data: data
  })
}

// 批量申请

export function batchAddApplyInfo(data) {
  return request({
    url: '/intensive/apply/batchAddApplyInfo',
    method: 'post',
    data: data
  })
}

// 我要申请提交时校验
export function validateProcessCompany(data) {
  return request({
    url: '/intensive/flowable/applay/validateProcessCompany',
    method: 'post',
    data: data
  })
}

// 预计使用时间修改监听

export function estimatedTimeAutoAddCompanyAndStaff(data) {
  return request({
    url: '/intensive/apply/estimatedTimeAutoAddCompanyAndStaff',
    method: 'post',
    data: data
  })
}

