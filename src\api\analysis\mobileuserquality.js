import request from '@/utils/request'
// 经分数据分析-移网用户发展质量
const BASE_URL = `/analysis/mobileuserquality`

export class MobileUserQualitySearch {
  /**
   * @description 获取地市列表
   * @params {*}
   * @return {*}
   */
  static getCityList () {
    return request({
      url: `/sys/borough/allNonProvincialCities`,
      method: 'post'
    })
  }

  /**
   * @description 获取数据列表
   * @params {*}
   * @return {*}
   */
  static queryMobileUserQualityIndexList (params) {
    return request({
      url: `${BASE_URL}/queryMobileUserQualityIndexList`,
      method: 'post',
      data: params
    })
  }

}
