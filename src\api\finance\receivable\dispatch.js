import request from '@/utils/request'

// 查询调度管理列表
export function listRole(params,query) {
  return request({
    url: '/finance/receivable/dispatch/list',
    method: 'post',
    data: query,
    params: params
  })
}

// 获取新增信息
export function getAdd() {
  return request({
    url: '/finance/receivable/dispatch/newReceivableDispatch',
    method: 'post'
  })
}

// 查询调度详细信息
export function detailListBydispatchId(query) {
  return request({
    url: '/finance/receivable/dispatch/detailListBydispatchId',
    method: 'post',
    data: query
  })
}

// 角色数据权限
export function returnIListForDispatch(query) {
  return request({
    url: '/finance/receivable/dispatch/returnIListForDispatch',
    method: 'post',
    data: query
  })
}

// 删除调度
export function dropDispatchById(query) {
  return request({
    url: '/finance/receivable/dispatch/dropDispatchById',
    method: 'post',
    data: query
  })
}

//05-1 更新调度内容信息
export function updateDispatchById(query) {
  return request({
    url: '/finance/receivable/dispatch/updateDispatchById',
    method: 'post',
    data: query
  })
}

// 删除
export function deleteDetailById(query) {
  return request({
    url: '/finance/receivable/dispatch/deleteDetailById',
    method: 'post',
    data: query
  })
}

// 保存选中的应付账款列表
export function saveReturnIListForDispatch(query) {
  return request({
    url: '/finance/receivable/dispatch/saveReturnIListForDispatch',
    method: 'post',
    data: query
  })
}

//10 一键全选添加调度应收账款信息
export function saveAllReturnIListForDispatch(query) {
  return request({
    url: '/finance/receivable/dispatch/saveAllReturnIListForDispatch',
    method: 'post',
    data: query
  })
}

// 09 提交调度前校验
export function validateDispatchForSubmit(query) {
  return request({
    url: '/finance/receivable/dispatch/validateDispatchForSubmit',
    method: 'post',
    data: query
  })
}

// 08 提交调度
export function submitForDispatch(query) {
  return request({
    url: '/finance/receivable/dispatch/submitForDispatch',
    method: 'post',
    data: query
  })
}

//09 获取应收账款明细查询
export function getInfo(query) {
  return request({
    url: '/finance/receivable/reportDetails/getInfo/'+query,
    method: 'post'
  })
}

// 10 历史调度信息查询
export function queryHistortInfo(query) {
  return request({
    url: '/finance/receivable/reportDetails/queryHistortInfo/'+query,
    method: 'post'
  })
}

//  11 本次调度信息查询
export function queryThisDispatchDetailInfo(query) {
  return request({
    url: '/finance/receivable/reportDetails/queryThisDispatchDetailInfo/'+query,
    method: 'post'
  })
}

// 12 保存应收账款明细
export function save(data) {
  return request({
    url: '/finance/receivable/reportDetails/save',
    method: 'put',
    data: data
  })
}

// 13 提交应收账款明细
export function validateAndSave(data) {
  return request({
    url: '/finance/receivable/reportDetails/validateAndSave',
    method: 'put',
    data: data
  })
}
