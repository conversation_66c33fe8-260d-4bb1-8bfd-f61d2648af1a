import request from '@/utils/request';

/**
 * 查询近期的扣收标准
 */
export function recentWithholdingStandard(data) {
  return request({
    url: "/finance/financeWithholding/recentWithholdingStandard",
    method: "post",
    data: data
  });
}

/**
 * 查询扣收明细数据
 * @param data
 */
export function withholdingDetailData(data, params) {
  return request({
    url: "/finance/financeWithholding/withholdingDetailData",
    method: "post",
    data: data,
    params: params
  });
}

/**
 * 查询扣收汇总数据
 * @param monthId
 */
export function withholdingStatisticsData(monthId) {
  return request({
    url: "/finance/financeWithholding/withholdingStatisticsData/" + monthId,
    method: "post"
  });
}

/**
 * 生成扣收数据
 * @param data
 */
export function generateWithholdingData(data) {
  return request({
    url: "/finance/financeWithholding/generateWithholdingData",
    method: "post",
    data: data
  });
}

/**
 * 保存扣收标准
 * @param data
 */
export function saveWithholdingStandard(data) {
  return request({
    url: "/finance/financeWithholding/saveWithholdingStandard",
    method: "post",
    data: data
  });
}
